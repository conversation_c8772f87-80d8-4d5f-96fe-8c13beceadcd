<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/*
| -------------------------------------------------------------------------
| URI ROUTING
| -------------------------------------------------------------------------
| This file lets you re-map URI requests to specific controller functions.
|
| Typically there is a one-to-one relationship between a URL string
| and its corresponding controller class/method. The segments in a
| URL normally follow this pattern:
|
|	example.com/class/method/id/
|
| In some instances, however, you may want to remap this relationship
| so that a different class/function is called than the one
| corresponding to the URL.
|
| Please see the user guide for complete details:
|
|	https://codeigniter.com/user_guide/general/routing.html
|
| -------------------------------------------------------------------------
| RESERVED ROUTES
| -------------------------------------------------------------------------
|
| There are three reserved routes:
|
|	$route['default_controller'] = 'welcome';
|
| This route indicates which controller class should be loaded if the
| URI contains no data. In the above example, the "welcome" class
| would be loaded.
|
|	$route['404_override'] = 'errors/page_missing';
|
| This route will tell the Router which controller/method to use if those
| provided in the URL cannot be matched to a valid route.
|
|	$route['translate_uri_dashes'] = FALSE;
|
| This is not exactly a route, but allows you to automatically route
| controller and method names that contain dashes. '-' isn't a valid
| class or method name character, so it requires translation.
| When you set this option to TRUE, it will replace ALL dashes in the
| controller and method URI segments.
|
| Examples:	my-controller/index	-> my_controller/index
|		my-controller/my-method	-> my_controller/my_method
*/
$route["master"] = "admin/Dashboard/index";
$route["master/dashboard"] = "admin/Dashboard/index";
$route["master/change-password"] = "admin/ChangePassword/index";
$route["master/password/update"] = "admin/ChangePassword/update";

$route["master/sales-professionals"] = "admin/SalesProfessionals/index";
$route["master/businesses"] = "admin/Businesses/index";
$route["master/orders"] = "admin/orders/index";

$route["master/blogs"] = "admin/Blogs/index";
$route["master/blogs/add"] = "admin/Blogs/add";
$route["master/blogs/save"] = "admin/Blogs/save";
$route["master/blogs/edit"] = "admin/Blogs/edit";
$route["master/blogs/update"] = "admin/Blogs/update";

$route["master/courses"] = "admin/Courses/index";
$route["master/promotion-settings"] = "admin/PromotionSettings/index";
$route['master/promotion-settings/add'] = 'admin/PromotionSettings/add';
$route['master/promotion-settings/edit/(:num)'] = 'admin/PromotionSettings/edit/$1';
$route['master/promotion-settings/delete/(:num)'] = 'admin/PromotionSettings/delete/$1';

$route['master/commission'] = 'admin/Commission/index';
$route['master/commission/add'] = 'admin/Commission/add';
$route['master/commission/edit/(:num)'] = 'admin/Commission/edit/$1';
$route['master/commission/delete/(:num)'] = 'admin/Commission/delete/$1';


// $route["master/vendors/menu-categories"] = "admin/vendors/menu_categories";
// $route["master/vendors/add-menu-category"] = "admin/vendors/add_menu_category";
// $route["master/vendors/save-menu-category"] = "admin/vendors/save_menu_category";
// $route["master/vendors/edit-menu-category"] = "admin/vendors/edit_menu_category";
// $route["master/vendors/update-menu-category"] = "admin/vendors/update_menu_category";
// $route["master/vendors/menu"] = "admin/vendors/menu";
// $route["master/vendors/add-menu"] = "admin/vendors/add_menu";
// $route["master/vendors/save-menu"] = "admin/vendors/save_menu";
// $route["master/vendors/edit-menu"] = "admin/vendors/edit_menu";
// $route["master/vendors/update-menu"] = "admin/vendors/update_menu";
// $route["master/vendors/license-keys"] = "admin/vendors/license_keys";
// $route["master/vendors/change-status"] = "admin/vendors/change_status";

// $route["master/reports"] = "admin/Reports/index";

$route['master/login'] = 'admin/Login/index';
$route['master/logout'] = 'admin/Logout/index';


// $route["vendor"] = "vendor/Dashboard/index";
// $route["vendor/dashboard"] = "vendor/Dashboard/index";
// $route["vendor/change-password"] = "vendor/ChangePassword/index";
// $route["vendor/password/update"] = "vendor/ChangePassword/update";

// $route["vendor/branches"] = "vendor/Branches/index";
// $route["vendor/branches/add"] = "vendor/Branches/add";
// $route["vendor/branches/save"] = "vendor/Branches/save";
// $route["vendor/branches/edit"] = "vendor/Branches/edit";
// $route["vendor/branches/update"] = "vendor/Branches/update";
// $route["vendor/branches/delete"] = "vendor/Branches/delete";

// $route['vendor/login'] = 'vendor/Login/index';
// $route['vendor/logout'] = 'vendor/Logout/index';

// $route["vendor/menu-categories"] = "vendor/MenuCategories/index";
// $route["vendor/menu-categories/add"] = "vendor/MenuCategories/add";
// $route["vendor/menu-categories/save"] = "vendor/MenuCategories/save";
// $route["vendor/menu-categories/edit"] = "vendor/MenuCategories/edit";
// $route["vendor/menu-categories/update"] = "vendor/MenuCategories/update";
// $route["vendor/menu-categories/delete"] = "vendor/MenuCategories/delete";

// $route["vendor/menu-items"] = "vendor/MenuItem/index";
// $route["vendor/menu-items/add"] = "vendor/MenuItem/add";
// $route["vendor/menu-items/save"] = "vendor/MenuItem/save";
// $route["vendor/menu-items/edit"] = "vendor/MenuItem/edit";
// $route["vendor/menu-items/update"] = "vendor/MenuItem/update";
// $route["vendor/menu-items/delete"] = "vendor/MenuItem/delete";

// $route["vendor/reports"] = "vendor/Reports/index";


// $route["vendor/qrcode-generator"] = "vendor/QRCodeGenerator/index";
// $route["vendor/license-keys"] = "vendor/LicenseKeys/index";
// $route["vendor/qrcode-generator/add"] = "vendor/QRCodeGenerator/add";
// $route["vendor/qrcode-generator/generate"] = "vendor/QRCodeGenerator/generate";
// $route["cart/(:num)"] = "Home/cart/$1";
// $route["checkout"] = "Home/checkout";
// $route["order-confirmation/(:any)"] = "Home/order_confirmation/$1";
// $route["order-details"] = "Home/order_details";
 $route["about-us"] = "StaticPages/aboutUs";
 $route["referrel-program-explanation"] = "StaticPages/referrel_program_explain";
 $route["profile-promotion-explanation"] = "StaticPages/profile_promotion_explain";
 $route["contact-us"] = "StaticPages/contactUs";
 $route["terms-and-conditions"] = "StaticPages/termAndCondition";
 $route["privacy-policy"] = "StaticPages/privacyPolicy";
 $route["explore-freelancers"] = "StaticPages/exploreFreelancers";
 $route["freelancer-details/(:num)"] = "StaticPages/freelancerDetails/$1";
 $route["find-business"] = "StaticPages/findBusiness";
 $route["business-details/(:num)"] = "StaticPages/businessDetails/$1";
 $route["leaderboard"] = "StaticPages/leaderboard";
 $route["checkout"] = "Checkout/index";
 $route["login"] = "Auth/login";
 $route["register"] = "Auth/register";
 $route['forgot-password'] = "Auth/forgot_password";
$route['forgot/send_reset_link'] = 'Auth/send_reset_link';
$route['forgot/reset_password/(:any)/(:any)'] = 'Auth/reset_password/$1/$2';
$route['forgot/update_password'] = 'Auth/update_password';

 $route['business/profile'] = "Business/profile";
 $route['business/assignment-history/(:num)'] = 'Business/assignment_history/$1';
 $route['business/assignmentBids/(:num)'] = 'Business/assignment_bids/$1';
 $route['business/assignmentBids/(:num)/(:num)'] = 'Business/assignment_bids/$1/$2';
 $route['business/orders'] = 'Business/orders';
 $route['inbox'] = 'Chat/index';
 $route['inbox/(:num)'] = 'Chat/index/$1';
 $route['withdraw'] = 'Withdraw/index';
 $route['sales/profile'] = "SalesProfessional/profile";
 $route['sales/earnings'] = "SalesProfessional/earnings";
 $route['sales/bid-history'] = "SalesProfessional/bid_history";
 $route['sales/orders'] = 'SalesProfessional/orders';
 $route['sales/withdraw'] = 'SalesProfessional/bank_accounts';
 $route['logout'] = "Auth/logout";

 $route['manage-subscription'] = "ManageSubscription/index";

 $route['assignments'] = "Assignments/index";
 $route['courses'] = "Course/index";
 $route['blogs'] = "Blogs/index";
$route['blogs/(:num)'] = "Blogs/index/$1";
$route['(:any)'] = "Blogs/single/$1";


// $route['(:any)'] = "StaticPages/blog/$1";
$route['default_controller'] = 'StaticPages/index';
$route['404_override'] = '';
$route['translate_uri_dashes'] = TRUE;
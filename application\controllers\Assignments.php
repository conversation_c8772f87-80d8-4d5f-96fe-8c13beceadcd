<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Assignments extends CI_Controller {

    public function __construct()
    {
        parent::__construct();
        $this->load->model('AssignmentModel');
        $this->load->library('pagination');
        
    }

    public function index($page = 0)
{
    $search = $this->input->get('search'); // get the search input

    // Pagination configuration
    $config['base_url'] = base_url('assignments/index');
    $config['total_rows'] = $this->AssignmentModel->get_assignments_count($search);
    $config['per_page'] = 10;
    $config['uri_segment'] = 3;
    $config['reuse_query_string'] = TRUE; // keep search term in pagination links

    // Bootstrap pagination styling
    $config['full_tag_open'] = '<nav aria-label="Page navigation"><ul class="pagination justify-content-center">';
    $config['full_tag_close'] = '</ul></nav>';
    $config['attributes'] = ['class' => 'page-link'];
    $config['first_link'] = 'First';
    $config['last_link'] = 'Last';
    $config['next_link'] = 'Next';
    $config['prev_link'] = 'Previous';
    $config['first_tag_open'] = '<li class="page-item">';
    $config['first_tag_close'] = '</li>';
    $config['last_tag_open'] = '<li class="page-item">';
    $config['last_tag_close'] = '</li>';
    $config['next_tag_open'] = '<li class="page-item">';
    $config['next_tag_close'] = '</li>';
    $config['prev_tag_open'] = '<li class="page-item">';
    $config['prev_tag_close'] = '</li>';
    $config['cur_tag_open'] = '<li class="page-item active"><a class="page-link" href="#">';
    $config['cur_tag_close'] = '</a></li>';
    $config['num_tag_open'] = '<li class="page-item">';
    $config['num_tag_close'] = '</li>';

    $this->pagination->initialize($config);

    $data['assignments'] = $this->AssignmentModel->get_assignments_with_business_details($config['per_page'], $page, $search);
    $data['pagination_links'] = $this->pagination->create_links();
    $data['search'] = $search;

    $this->load->Template('assignments/list', $data);
}



    public function details($assignment_id) {
        $data['assignment'] = $this->AssignmentModel->get_assignment_by_id($assignment_id);
        $this->load->Template('assignments/details', $data);
    }


    public function place_bid($assignment_id) {
        if (!$this->session->userdata('s_id')) {
            redirect('login');
        }
        $data['assignment'] = $this->AssignmentModel->get_assignment_by_id($assignment_id);
        $this->load->Template('assignments/place_bid', $data);
    }
    
    public function submit_bid() {
    $assignment_id = $this->input->post('assignment_id');
    $sales_professional_id = $this->session->userdata('s_id');
    $payment_type = $this->input->post('payment_type');

    if ($this->AssignmentModel->check_existing_bid($assignment_id, $sales_professional_id)) {
        $this->session->set_flashdata('bid_error', 'You have already placed a bid on this job.');
    } else {
        $bid_data = array(
            'assignment_id' => $assignment_id,
            'sales_professional_id' => $sales_professional_id,
            'deadline' => $this->input->post('deadline'),
            'description' => $this->input->post('bid_description'),
            'payment_type' => $payment_type,
            'created_at' => date('Y-m-d H:i:s')
        );

        if ($payment_type === 'hourly' || $payment_type === 'hourly_bonus') {
            $bid_data['hourly_rate'] = $this->input->post('hourly_rate');
        }
        if ($payment_type === 'hourly_bonus') {
            $bid_data['hourly_bonus'] = $this->input->post('hourly_bonus');
        }
        if ($payment_type === 'result_based') {
            $bid_data['commission_terms'] = $this->input->post('commission_terms');
        }

        if ($this->AssignmentModel->insert_bid($bid_data)) {
            $this->session->set_flashdata('bid_success', 'Bid placed successfully.');
        } else {
            $this->session->set_flashdata('bid_error', 'An error occurred while placing the bid.');
        }
    }

    redirect('assignments');
}

    
    
    
    
}

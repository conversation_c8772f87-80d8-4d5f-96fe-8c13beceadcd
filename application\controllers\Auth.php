<?php
defined('BASEPATH') or exit('No direct script access allowed');
class Auth extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        $this->load->model('UserModel');
        $this->load->library('email');

    }
    public function login()
    {
        $data = array();
        $this->load->Template('account/login', $data);  
    }

    public function register()
    {
        $data = array();
        $this->load->Template('account/register', $data);  
    }

    public function signup() {
        $userType = $this->input->post('user_type');
        $email = $this->input->post('email');
        $verify = $this->UserModel->checkEmailExists($email, $userType);
    
        if (!$verify) {
            $data = [
                'email' => $email,
                'kvk_vat' => $this->input->post('kvk_vat'),
                'password' => password_hash($this->input->post('password'), PASSWORD_DEFAULT),
                'created_at' => date('Y-m-d H:i:s'),
            ];
    
            if ($userType == 'business') {
                $data['business_name'] = $this->input->post('business_name');
                $data['industry'] = $this->input->post('industry');
                $tableName = 'businesses';
            } elseif ($userType == 'sales_professional') {
                $data['full_name'] = $this->input->post('full_name');
                $data['location'] = $this->input->post('location');
                $data['industry'] = $this->input->post('industry');
                
                $tableName = 'sales_professionals';
            }
    
            $user = $this->UserModel->signup($data, $tableName);
    
            if ($user) {
                $insert_id = $this->db->insert_id();
                $referral_code = 'REF' . strtoupper(substr(md5($insert_id . time()), 0, 6));
                $update_data = ['referral_code' => $referral_code];
    
                if ($userType == 'sales_professional') {
                    $submitted_referral_code = $this->input->post('referral_code');
                    $referred_by = NULL;
    
                    if (!empty($submitted_referral_code)) {
                        $referrer = $this->db->get_where('sales_professionals', ['referral_code' => $submitted_referral_code])->row();
    
                        if ($referrer) {
                            $referred_by = $referrer->id;
                        }
                    }
    
                    $update_data['referred_by'] = $referred_by;
    
                    if ($referred_by) {
                        $this->db->insert('freelancer_referrals', [
                            'referrer_id' => $referred_by,
                            'referred_freelancer_id' => $insert_id,
                            'created_at' => date('Y-m-d H:i:s'),
                            'expires_at' => date('Y-m-d H:i:s', strtotime('+6 months'))
                        ]);
                    }
                }
    
                if ($userType == 'business') {
                    $submitted_referral_code = $this->input->post('referral_code');
                    $referred_by = NULL;
    
                    if (!empty($submitted_referral_code)) {
                        $referrer = $this->db->get_where('sales_professionals', ['referral_code' => $submitted_referral_code])->row();
    
                        if ($referrer) {
                            $referred_by = $referrer->id;
                            $update_data['referred_by'] = $referred_by;
                            $update_data['referral_start_date'] = date('Y-m-d H:i:s');
                            $update_data['referral_end_date'] = date('Y-m-d H:i:s', strtotime('+6 months'));
    
                            $this->db->insert('business_referrals', [
                                'referrer_id' => $referred_by,
                                'referred_business_id' => $insert_id,
                                'created_at' => $update_data['referral_start_date'],
                                'expires_at' => $update_data['referral_end_date']
                            ]);
                        }
                    }
                }
    
                $this->db->where('id', $insert_id);
                $this->db->update($tableName, $update_data);
    
                // Send verification email
                $this->sendVerificationEmail($email, $userType, $tableName);
    
                echo json_encode(['status' => 'success', 'message' => 'Registration successful. Please check your email to verify your account.']);
            } else {
                echo json_encode(['status' => 'error', 'message' => 'Registration failed. Please try again.']);
            }
        } else {
            echo json_encode(['status' => 'error', 'message' => 'Email already exists!']);
        }
    }
    

    
    public function sendVerificationEmail($email, $userType, $tableName) {
        $this->load->library('email');
    
        // Generate verification token
        $token = bin2hex(random_bytes(32));
    
        // Save the verification token to the relevant table
        $this->UserModel->saveVerification($email, $token, $userType, $tableName);
    
        // Generate the verification link
        $link = base_url() . "Auth/verifyUser?verify-account-token=" . $token . "&user_type=" . $userType;
    
        // Prepare email content
        $arr["link"] = $link;
        $arr["username"] = $email; // You may want to get the user's name or business name based on the table
    
        // Load email template
        $template = $this->load->view('account/verify-account', $arr, true);
    
        // Email configuration
        $config = [
            'protocol' => 'smtp',
            'smtp_host' => 'ssl://smtp.hostinger.com',
            'smtp_port' => 465,
            'smtp_user' => '<EMAIL>',
            'smtp_pass' => 'Watisbeter?2025',
            'mailtype' => 'html',
            'charset' => 'utf-8',
            'wordwrap' => TRUE,
             'newline'   => "\r\n"
        ];
    
        $this->email->initialize($config);
        $this->email->from("<EMAIL>");
        $this->email->to($email);
        $this->email->subject('Verify User Account');
        $this->email->message($template);
        
        // Send email
        if ($this->email->send()) {
            return true;
        } else {
            echo $this->email->print_debugger();
            return false;
        }
    }
    
    public function verifyUser() {
        // Get the token and user type from the URL
        $token = $this->input->get('verify-account-token');
        $userType = $this->input->get('user_type');

        if (empty($token) || empty($userType)) {
            show_error('Invalid verification link');
        }

        // Check the token and user type in the database
        $user = $this->UserModel->verifyAccountToken($token, $userType);

        if ($user) {
            // If the token is valid, update the account as verified
            $this->UserModel->updateVerificationStatus($user->email, $userType);

            // Redirect the user or show a success message
            redirect('login');
        } else {
            show_error('Invalid verification link');
        }
    }
    
    public function do_login() {
        $remember = $this->input->post('remember');
        $email = $this->input->post('email');
        $password = $this->input->post('password');
        $user_type = $this->input->post('user_type');
        $year = time() + ********;
        
        if (isset($remember)) {
            set_cookie("sales_email", $email, $year);
            set_cookie("sales_password", $password, $year);
            if ($user_type == 'business') {
                set_cookie("business_remember", "active", $year);
            } else {
                set_cookie("sales_remember", "active", $year);
            }
        }

        if ($user_type == 'business') {
            $result = $this->UserModel->businessLogin($email);
            
            if (!empty($result)) {
                $user = $result[0];
                
                // Verify the provided password with the hashed password from the database
                if (password_verify($password, $user->password)) {
                    $this->session->set_userdata('b_id', $user->id);
                    $this->session->set_userdata('name', $user->business_name);
                    $this->session->set_userdata('email', $user->email);
                    $this->session->set_userdata('user_type', 'business');
        
                    $this->session->set_flashdata('success', 'User Login Successfully');
                    redirect('business/profile');
                } else {
                    $this->session->set_flashdata('login_error', "Incorrect username & password");
                    redirect('login');
                }
            } else {
                $this->session->set_flashdata('login_error', "Incorrect username & password");
                redirect('login');
            }
        }
        else{
            $result = $this->UserModel->salesLogin($email);
            
            if (!empty($result)) {
                $user = $result[0];
                
                // Verify the provided password with the hashed password from the database
                if (password_verify($password, $user->password)) {
                    $this->session->set_userdata('s_id', $user->id);
                    $this->session->set_userdata('name', $user->name);
                    $this->session->set_userdata('email', $user->email);
                    $this->session->set_userdata('stripe_customer_id', $user->stripe_customer_id);
                     $this->session->set_userdata('user_type', 'sales_professional');
        
                    $this->session->set_flashdata('success', 'User Login Successfully');
                    redirect('sales/profile');
                } else {
                    $this->session->set_flashdata('login_error', "Incorrect username & password");
                    redirect('login');
                }
            } else {
                $this->session->set_flashdata('login_error', "Incorrect username & password");
                redirect('login');
            }
        }
    }
    

    public function logout(){
        
        $data = $this->session->all_userdata();
        foreach($data as $row => $rows_value)
        {
            $this->session->unset_userdata($row);
        }
        redirect('login');
    }
    
    public function forget_password(){
        $data = [];
		$this->load->Template('account/forget_password',$data);
    }

    public function find_password(){
        $email = $this->input->post("email");
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $this->session->set_flashdata('forget_error',"Invalid email format!");
            redirect('forget-password');
        } 
        else{
            $data = $this->UserModel->find_password($email);
            if(isset($data["password"])){
                $this->send_mail($email, $data["password"]);
                $this->session->set_flashdata('forget_success',"Password has been Sent to your Email");
                redirect('forget-password');
            }

        }
    }

    private function send_mail($email,$message){
        $config = [
            'protocol' => 'smtp',
            'smtp_host' => 'ssl://smtp.hostinger.com',
            'smtp_port' => 465,
            'smtp_user' => '<EMAIL>',
            'smtp_pass' => 'Watisbeter?2025',
            'mailtype' => 'html',
            'charset' => 'utf-8',
            'wordwrap' => TRUE,
             'newline'   => "\r\n"
        ];
        $send_from_address = "<EMAIL>";
        $send_to_address = $email;
        $subject = "Password Reset";

        //$this->load->library('email',$config);
        $this->email->initialize($config);

        $this->email->from($send_from_address);
        $this->email->to($send_to_address);
        $this->email->subject($subject);
        $this->email->message($message);
        if($this->email->send()){
				
        }
        else{
          //  echo $this->email->print_debugger();exit;
        }
    }
  

    public function forgot_password() {
        $data = [];
        $this->load->Template('account/forgot_password', $data);
    }

    public function send_reset_link() {
        $email = $this->input->post('email');
        $type = $this->input->post('user_type');

        $user = $this->UserModel->get_user_by_email($email, $type);

        if ($user) {
            $token = bin2hex(random_bytes(50));
            $expiry = date('Y-m-d H:i:s', strtotime('+1 hour'));

            $this->UserModel->store_reset_token($email, $type, $token, $expiry);

            $reset_link = base_url("auth/reset_password/$type/$token");
            $message = "Click this link to reset your password: $reset_link";

            // Send email
            $this->send_mail($email, $message);

            $this->session->set_flashdata('success', 'Reset link sent to your email.');
        } else {
            $this->session->set_flashdata('error', 'Email not found.');
        }

        redirect('forgot-password');
    }

    public function reset_password($type, $token) {
        $user = $this->UserModel->get_user_by_token($type, $token);

        if (!$user) {
            $this->session->set_flashdata('error', 'Invalid or expired token.');
            redirect('forgot-password');
        }

        $data['user_type'] = $type;
        $data['token'] = $token;
        $this->load->Template('account/reset_password', $data);
    }

    public function update_password() {
        $type = $this->input->post('user_type');
        $token = $this->input->post('token');
        $password = password_hash($this->input->post('password'), PASSWORD_DEFAULT);

        $user = $this->UserModel->get_user_by_token($type, $token);

        if (!$user) {
            $this->session->set_flashdata('error', 'Invalid or expired token.');
            redirect('forgot-password');
        }

        $this->UserModel->update_password($type, $token, $password);
        $this->session->set_flashdata('success', 'Password updated. Please login.');
        redirect('login');
    }
   


}
?>
<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Blogs extends CI_Controller {
	public function __construct(){
		parent::__construct();
    
		$this->load->model("BlogsModel");
	}
   
	public function index(){
		$data = [];
		$this->load->library('pagination');
	
		// Pagination Config
		$config['base_url'] = base_url('blogs');
		$config['total_rows'] = $this->BlogsModel->countAll();
		$config['per_page'] = 6;
		$config['uri_segment'] = 2;
		$config['num_links'] = 2;
	
		// Bootstrap Success Pagination Styling
		$config['full_tag_open'] = '<ul class="pagination justify-content-center">';
		$config['full_tag_close'] = '</ul>';
	
		$config['first_link'] = 'First';
		$config['first_tag_open'] = '<li class="page-item">';
		$config['first_tag_close'] = '</li>';
	
		$config['last_link'] = 'Last';
		$config['last_tag_open'] = '<li class="page-item">';
		$config['last_tag_close'] = '</li>';
	
		$config['next_link'] = '&raquo;';
		$config['next_tag_open'] = '<li class="page-item">';
		$config['next_tag_close'] = '</li>';
	
		$config['prev_link'] = '&laquo;';
		$config['prev_tag_open'] = '<li class="page-item">';
		$config['prev_tag_close'] = '</li>';
	
		$config['cur_tag_open'] = '<li class="page-item active"><a class="page-link bg-success border-success text-white">';
		$config['cur_tag_close'] = '</a></li>';
	
		$config['num_tag_open'] = '<li class="page-item">';
		$config['num_tag_close'] = '</li>';
	
		$config['attributes'] = ['class' => 'page-link btn btn-outline-success']; // Makes the entire button clickable
	
		$this->pagination->initialize($config);
	
		$page = ($this->uri->segment(2)) ? $this->uri->segment(2) : 0;
	
		$data["list"] = $this->BlogsModel->getPaginated($config["per_page"], $page);
		$data["pagination_links"] = $this->pagination->create_links();
	
		$this->load->Template('static_pages/blogs', $data);
	}
	
	

	public function single($route){
		$data=[];
		
		
		$blog = $this->BlogsModel->getByRoute($route);
		if(!isset($blog)){
            redirect("404");
			exit;
		}
		
		$meta = [];
		$meta['title'] = $blog->title;
		$meta['description'] = $blog->desc;
		$meta['keywords'] = $blog->route;
		$meta['route']='blogs';
		
		$data["blog"] = $blog;
		$data["meta"] = $meta;
		
		$data["latest_blogs"] = $this->BlogsModel->getLatestBlogs($route, 5);

		$this->load->Template('static_pages/single', $data);
	}
}
<?php
defined('BASEPATH') or exit('No direct script access allowed');

require_once FCPATH . 'vendor/autoload.php';

use Stripe\Stripe;
use Stripe\PaymentIntent;

class Business extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        if (!$this->session->userdata('b_id')) {
            redirect('login');
        }
       
        $this->load->model('BusinessModel');
        $this->load->model('PromotionModel');
        $this->load->library('pagination');
        $this->load->helper('stripe_helper');
        Stripe::setApiKey('***********************************************************************************************************');
        
    }
    
    public function profile()
    {
        
        $data = array();
        $data['business'] = $this->BusinessModel->get_business_by_id($this->session->userdata('b_id'));
        $this->load->Template('business/profile', $data);  
    }

    public function editProfile() {
        $data = [
            'business_name' => $this->input->post('business_name'),
            'industry' => $this->input->post('industry'),
            'goals' => $this->input->post('goals'),
            'kvk_vat' => $this->input->post('kvk_vat')
        ];
        if (!empty($_FILES['profile_photo']['name'])) {
            $config['upload_path'] = './assets/img/business/';
            $config['allowed_types'] = 'gif|jpg|png';
            $config['max_size'] = 2048;

            $this->load->library('upload', $config);

            if (!$this->upload->do_upload('profile_photo')) {
                $error = ['error' => $this->upload->display_errors()];
                $this->session->set_flashdata('update_error', $error);
            } else {
                $data['profile_picture'] = $this->upload->data('file_name');
            }
        }
        if ($this->BusinessModel->update_business($this->session->userdata('b_id'), $data)) {
            $this->session->set_flashdata('update_success', 'Profile updated successfully.');
        } else {
            $this->session->set_flashdata('update_error', 'Failed to update profile. Please try again.');
        }
    redirect('business/profile');
    }



    public function assignment_history($offset = 0)
    {
        $business_user_id = $this->session->userdata('b_id');
        // Load pagination library and initialize configuration
        $this->load->library('pagination');
        $config = [
            'base_url' => base_url('business/assignment-history'),
            'total_rows' => $this->BusinessModel->get_count($business_user_id),
            'per_page' => 5,
            'uri_segment' => 3,
            'full_tag_open' => '<nav><ul class="pagination justify-content-center">',
            'full_tag_close' => '</ul></nav>',
            'num_tag_open' => '<li class="page-item">',
            'num_tag_close' => '</li>',
            'cur_tag_open' => '<li class="page-item active"><a class="page-link">',
            'cur_tag_close' => '</a></li>',
            'next_tag_open' => '<li class="page-item">',
            'next_tag_close' => '</li>',
            'prev_tag_open' => '<li class="page-item">',
            'prev_tag_close' => '</li>',
            'first_tag_open' => '<li class="page-item">',
            'first_tag_close' => '</li>',
            'last_tag_open' => '<li class="page-item">',
            'last_tag_close' => '</li>',
            'attributes' => array('class' => 'page-link')
        ];

        $this->pagination->initialize($config);

        // Fetch assignments for the current page
        $data['assignments'] = $this->BusinessModel->get_assignments_with_bid_count($business_user_id, $config['per_page'], $page);;

        // Pass the pagination links to the view
        $data['pagination_links'] = $this->pagination->create_links();

        // Load the view with the assignments and pagination
        $this->load->template('business/assignment/list', $data);
    }

    public function assignment_bids($assignment_id, $page = 0) {
       
        $this->load->library('pagination');
        $config = [
            'base_url' => base_url('business/assignmentBids/' . $assignment_id),
            'total_rows' => $this->BusinessModel->get_bids_count($assignment_id),
            'per_page' => 10,
            'uri_segment' => 4,
            'full_tag_open' => '<nav><ul class="pagination justify-content-center">',
            'full_tag_close' => '</ul></nav>',
            'num_tag_open' => '<li class="page-item">',
            'num_tag_close' => '</li>',
            'cur_tag_open' => '<li class="page-item active"><a class="page-link">',
            'cur_tag_close' => '</a></li>',
            'next_tag_open' => '<li class="page-item">',
            'next_tag_close' => '</li>',
            'prev_tag_open' => '<li class="page-item">',
            'prev_tag_close' => '</li>',
            'first_tag_open' => '<li class="page-item">',
            'first_tag_close' => '</li>',
            'last_tag_open' => '<li class="page-item">',
            'last_tag_close' => '</li>',
            'attributes' => array('class' => 'page-link')
        ];

        $this->pagination->initialize($config);
    
        $data['bids'] = $this->BusinessModel->get_bids_for_assignment($assignment_id, $config['per_page'], $page);
        $data['pagination'] = $this->pagination->create_links();
        $data['assignment'] = $this->BusinessModel->get_assignment_by_id($assignment_id);
       
    
        $this->load->Template('business/assignment/assignment_bids', $data);
    }
    


    public function assignmentUpload()
{
    if ($this->input->post()) {
        $data = [
            'title' => $this->input->post('title'),
            'description' => $this->input->post('description'),
            'budget' => $this->input->post('budget') ?: NULL,
            'deadline' => $this->input->post('deadline'),
            'user_id' => $this->session->userdata('b_id'),
            'is_public' => $this->input->post('is_public') ? 1 : 0,
            'pricing_type' => $this->input->post('pricing_type'),
            'payment_method' => $this->input->post('payment_method'),
        ];

        // Pricing-related fields
        if ($data['pricing_type'] === 'hourly' || $data['pricing_type'] === 'hourly_bonus') {
            $data['hourly_rate'] = $this->input->post('hourly_rate');
        }

        if ($data['pricing_type'] === 'hourly_bonus') {
            $data['bonus_details'] = $this->input->post('bonus_details');
        }

        if ($data['pricing_type'] === 'commission') {
            $data['commission_details'] = $this->input->post('commission_details');
        }

        // Optional file upload
        if (!empty($_FILES['file']['name'])) {
            $config['upload_path'] = './assets/img/assignments/attachments/';
            $config['allowed_types'] = 'pdf|doc|docx|txt';
            $this->load->library('upload', $config);

            if ($this->upload->do_upload('file')) {
                $fileData = $this->upload->data();
                $data['file'] = $fileData['file_name'];
            } else {
                $data['error'] = $this->upload->display_errors();
                return;
            }
        }

        // Banner upload
        if (!empty($_FILES['banner']['name'])) {
            $config['upload_path'] = './assets/img/assignments/banners/';
            $config['allowed_types'] = 'png|jpg|gif';
            $this->load->library('upload', $config);

            if ($this->upload->do_upload('banner')) {
                $fileData = $this->upload->data();
                $data['banner'] = $fileData['file_name'];
            } else {
                $data['error'] = $this->upload->display_errors();
                return;
            }
        }

        $this->BusinessModel->insert_assignment($data);
        redirect('business/assignment-history');
    } else {
        $this->load->Template('business/assignment/upload');
    }
}


    public function assignmentDetails($assignment_id) {
        $data['assignment'] = $this->BusinessModel->get_assignment_by_id($assignment_id);
        $data['promotion_settings'] = $this->PromotionModel->get_all();
        $this->load->Template('business/assignment/details', $data);
    }

    public function assignmentEdit($id)
    {
        $assignment = $this->BusinessModel->get_assignment($id, $this->session->userdata('b_id'));
        if (!$assignment) {
            show_404();
        }

        if ($this->input->post()) {
            $data = [
                'title' => $this->input->post('title'),
                'description' => $this->input->post('description'),
                'budget' => $this->input->post('budget') ?: NULL,
                'deadline' => $this->input->post('deadline'),
                'is_public' => $this->input->post('is_public') ? 1 : 0,
                'pricing_type' => $this->input->post('pricing_type'),
                'payment_method' => $this->input->post('payment_method'),
            ];

            // Pricing-related fields
        if ($data['pricing_type'] === 'hourly' || $data['pricing_type'] === 'hourly_bonus') {
            $data['hourly_rate'] = $this->input->post('hourly_rate');
        }

        if ($data['pricing_type'] === 'hourly_bonus') {
            $data['bonus_details'] = $this->input->post('bonus_details');
        }

        if ($data['pricing_type'] === 'commission') {
            $data['commission_details'] = $this->input->post('commission_details');
        }

            if (!empty($_FILES['file']['name'])) {
                $config['upload_path'] = './assets/img/assignments/attachments/';
                $config['allowed_types'] = 'pdf|doc|docx|txt';
                $this->load->library('upload', $config);

                if ($this->upload->do_upload('file')) {
                    $fileData = $this->upload->data();
                    $data['file'] = $fileData['file_name'];
                } else {
                    $data['error'] = $this->upload->display_errors();
                    return;
                }
            }

            if (!empty($_FILES['banner']['name'])) {
                $config['upload_path'] = './assets/img/assignments/banners/';
                $config['allowed_types'] = 'png|jpg|gif';
                $this->load->library('upload', $config);

                if ($this->upload->do_upload('banner')) {
                    $fileData = $this->upload->data();
                    $data['banner'] = $fileData['file_name'];
                } else {
                    $data['error'] = $this->upload->display_errors();
                    return;
                }
            }

            $this->BusinessModel->update_assignment($id, $data);
            redirect('business/assignment-history');
        } else {
            $this->load->Template('business/assignment/edit', compact('assignment'));
        }
    }

    public function orders() {
        
        $data['in_progress'] = $this->BusinessModel->get_orders('in-progress');
        $data['completed'] = $this->BusinessModel->get_orders('completed');
        $data['cancelled'] = $this->BusinessModel->get_orders('cancelled');
        $this->load->Template('business/orders/index', $data);
    }

    public function complete_order() {
        ob_start();
        error_reporting();
        $order_id = $this->input->post('order_id');
    
        $order = $this->db->get_where("orders", ["id" => $order_id])->row();
        if (!$order) {
            echo json_encode(['success' => false, 'message' => 'Order not found.']);
            return;
        }
    
        $order_amount = $order->amount;
        $freelancer_id = $order->freelancer_id;
    
        // Calculate 15% fee (platform profit) and remaining amount
        $platform_profit = $order_amount * 0.15;
        $remaining_amount = $order_amount - $platform_profit;
        $table = 'sales_professionals'; 

        $update_balance = update_user_balance($freelancer_id, $table, $remaining_amount);
        if (!$update_balance) {
            echo json_encode(['success' => false, 'message' => 'Failed to update balance.']);
            return;
        }
    
        // Mark Order as Complete
        $result = $this->BusinessModel->mark_as_complete($order_id);
    
        if ($result) {
            // === SALES PROFESSIONAL REFERRAL LOGIC ===
            $referral = $this->db->select('*')
                ->from('freelancer_referrals')
                ->where('referred_freelancer_id', $freelancer_id)
                ->where('expires_at >=', date('Y-m-d H:i:s'))
                ->get()
                ->row();
    
            if ($referral) {
                $referrer_id = $referral->referrer_id;
                $referrer_earning = $platform_profit * 0.20;
    
                $this->db->insert('freelancer_referral_earnings', [
                    'referrer_id' => $referrer_id,
                    'referred_freelancer_id' => $freelancer_id,
                    'assignment_id' => $order->assignment_id ?? NULL,
                    'platform_profit' => $platform_profit,
                    'referrer_earning' => $referrer_earning,
                    'created_at' => date('Y-m-d H:i:s')
                ]);
    
                $referrer = $this->db->get_where("sales_professionals", ["id" => $referrer_id])->row();
                update_user_balance($referrer->id, $table, $referrer_earning);
            }
    
            // === BUSINESS REFERRAL LOGIC ===
            $business_referral = $this->db->select('*')
                ->from('business_referrals')
                ->where('referred_business_id', $order->business_id) // assuming orders table has business_id
                ->where('expires_at >=', date('Y-m-d H:i:s'))
                ->get()
                ->row();
    
            if ($business_referral) {
                $referrer_business_id = $business_referral->referrer_id;
                $referrer_earning = $platform_profit * 0.20;
    
                $this->db->insert('company_referral_earnings', [
                    'referrer_id' => $referrer_business_id,
                    'referred_business_id' => $order->business_id,
                    'assignment_id' => $order->assignment_id ?? NULL,
                    'platform_profit' => $platform_profit,
                    'referrer_earning' => $referrer_earning,
                    'created_at' => date('Y-m-d H:i:s')
                ]);
    
                $referrer = $this->db->get_where("sales_professionals", ["id" => $referrer_business_id])->row();
                update_user_balance($referrer->id, $table, $referrer_earning);
            }
    
            echo json_encode(['success' => true]);
        } else {
            echo json_encode(['success' => false, 'message' => 'Failed to update order status.']);
        }
    }
    

    public function complete_result_based_order()
{
    $order_id = $this->input->post('order_id');
    $amount = $this->input->post('amount');
    $payment_method_id = $this->input->post('payment_method_id')? $this->input->post('payment_method_id'):"";
    $payment_intent_id = $this->input->post('payment_intent_id') ? $this->input->post('payment_intent_id'):""; // Only sent when confirming

    $order = $this->db->get_where('orders', ['id' => $order_id])->row();
    if (!$order) {
        echo json_encode(['success' => false, 'message' => 'Order not found.']);
        return;
    }

    $freelancer_id = $order->freelancer_id;
    $freelancer = $this->db->get_where('sales_professionals', ['id' => $freelancer_id])->row();

    if (empty($freelancer->stripe_customer_id)) {
        $stripe_customer_id = create_stripe_customer($freelancer_id);
        $this->db->where('id', $freelancer_id)->update('sales_professionals', ['stripe_customer_id' => $stripe_customer_id]);
    } else {
        $stripe_customer_id = $freelancer->stripe_customer_id;
    }

    $service_fee = $amount * 0.025;
    $total_amount = $amount + $service_fee;
    $platform_profit = $amount * 0.15;
    $remaining_amount = $amount - $platform_profit;

    try {
        if ($payment_intent_id && !empty($payment_intent_id)) {
            $paymentIntent = \Stripe\PaymentIntent::retrieve($payment_intent_id);
            if ($paymentIntent->status === 'succeeded') {
                $this->handle_successful_payment($order_id, $platform_profit, $remaining_amount, $stripe_customer_id, $order);
                echo json_encode(['success' => true]);
                return;
            } else {
                echo json_encode(['success' => false, 'message' => 'Payment confirmation failed.']);
                return;
            }
        } else {
            // === Step 1: Create + confirm intent ===
            $paymentIntent = \Stripe\PaymentIntent::create([
                'amount' => $total_amount * 100,
                'currency' => 'usd',
                'payment_method' => $payment_method_id,
                'confirmation_method' => 'manual',
                'confirm' => true,
                'setup_future_usage' => 'off_session',
            ]);

            if ($paymentIntent->status === 'requires_action') {
                echo json_encode([
                    'success' => false,
                    'requires_action' => true,
                    'payment_intent_client_secret' => $paymentIntent->client_secret,
                    'payment_intent_id' => $paymentIntent->id
                ]);
                return;
            } elseif ($paymentIntent->status === 'succeeded') {
                $this->handle_successful_payment($order_id, $platform_profit, $remaining_amount, $stripe_customer_id, $order);
                echo json_encode(['success' => true]);
                return;
            } else {
                echo json_encode(['success' => false, 'message' => 'Unexpected payment status.']);
                return;
            }
        }
    } catch (\Exception $e) {
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
}



private function handle_successful_payment($order_id, $platform_profit, $remaining_amount, $freelancer_stripe_id, $order)
{
    $this->BusinessModel->mark_as_complete($order_id);
    $table= "sales_professionals";
    update_user_balance($order->freelancer_id, $table, $remaining_amount);

    // === FREELANCER REFERRAL ===
    $referral = $this->db->select('*')->from('freelancer_referrals')
        ->where('referred_freelancer_id', $order->freelancer_id)
        ->where('expires_at >=', date('Y-m-d H:i:s'))
        ->get()->row();

    if ($referral) {
        $referrer_earning = $platform_profit * 0.20;
        $this->db->insert('freelancer_referral_earnings', [
            'referrer_id' => $referral->referrer_id,
            'referred_freelancer_id' => $order->freelancer_id,
            'assignment_id' => $order->assignment_id ?? NULL,
            'platform_profit' => $platform_profit,
            'referrer_earning' => $referrer_earning,
            'created_at' => date('Y-m-d H:i:s')
        ]);

        $referrer = $this->db->get_where("sales_professionals", ["id" => $referral->referrer_id])->row();
        update_user_balance($referrer->id, $table, $referrer_earning);
        
    }

    // === BUSINESS REFERRAL ===
    $business_referral = $this->db->select('*')->from('business_referrals')
        ->where('referred_business_id', $order->business_id)
        ->where('expires_at >=', date('Y-m-d H:i:s'))
        ->get()->row();

    if ($business_referral) {
        $referrer_earning = $platform_profit * 0.20;
        $this->db->insert('company_referral_earnings', [
            'referrer_id' => $business_referral->referrer_id,
            'referred_business_id' => $order->business_id,
            'assignment_id' => $order->assignment_id ?? NULL,
            'platform_profit' => $platform_profit,
            'referrer_earning' => $referrer_earning,
            'created_at' => date('Y-m-d H:i:s')
        ]);

        $referrer = $this->db->get_where("sales_professionals", ["id" => $business_referral->referrer_id])->row();
        update_user_balance($referrer->id, $table, $referrer_earning);
    }
}
    
    

    public function cancel_order() {
        $order_id = $this->input->post('order_id');
    
        $result = $this->BusinessModel->mark_as_cancel($order_id);
        
        if ($result) {
            echo json_encode(['success' => true]);
        } else {
            echo json_encode(['success' => false, 'message' => 'Failed to update order status.']);
        }
    }

    public function submit_review() {
        $order_id = $this->input->post('order_id');
        $sp_id = $this->input->post('sp_id');
        $rating = $this->input->post('rating');
        $review = $this->input->post('review');
        $user_id = $this->session->userdata('b_id');

        $data = [
            'order_id' => $order_id,
            'user_id' => $user_id,
            's_id' => $sp_id,
            'rating' => $rating,
            'review' => $review,
            'created_at' => date('Y-m-d H:i:s')
        ];


        $this->db->insert('reviews', $data);

        echo json_encode(['status' => 'success']);
    }

}
?>
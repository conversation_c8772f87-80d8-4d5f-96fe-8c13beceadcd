<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Chat extends CI_Controller {

    public function __construct() {
        parent::__construct();
        if (!$this->session->userdata('b_id') && !$this->session->userdata('s_id')) {
            redirect('login');
        }
        $this->load->model('ChatModel');
    }

    public function index()
    {
        $data = array();
        if($this->session->userdata('b_id')){
            $id = $this->session->userdata('b_id');
            $data['chats'] = $this->ChatModel->getBusinessChat($id); 
        }
        else{
            $id = $this->session->userdata('s_id');
            $data['chats'] = $this->ChatModel->getSalesChat($id);
        } 
        $this->load->Template('chat/index', $data);
    }


    public function start_chat() {
        $business_id = $this->input->post('business_id');
        $sales_professional_id = $this->input->post('sales_professional_id');
        $message = $this->input->post('message');

        if (contains_restricted_content($message)) {
            echo json_encode(['status' => false, 'message' => 'Sharing contact details is not allowed!']);
            return;
        }

        // Check if chat already exists
        $chat = $this->ChatModel->check_existing_chat($business_id, $sales_professional_id);

        if ($chat) {
            // If chat exists, return chat ID to redirect
             $msg_data = [
            'chat_id' => $chat->id,
            'sender_id' => $business_id, // Business is the sender
            'message' => $message
            ];
            $this->db->insert('messages', $msg_data);

            echo json_encode(["status" => "exists", "chat_id" => $chat->id]);
        } else {
            // Create new chat and insert message
            $chat_id = $this->ChatModel->create_chat($business_id, $sales_professional_id, $message);
            echo json_encode(["status" => "new", "chat_id" => $chat_id]);
        }
    }

    
    public function getChats($user_id) {
        $chats = $this->ChatModel->get_user_chats($user_id);
        echo json_encode(['status' => true, 'chats' => $chats]);
    }

    
    public function getMessages($chat_id) {
        $messages = $this->ChatModel->get_chat_messages($chat_id);
        echo json_encode(['status' => true, 'messages' => $messages]);
    }

   
    public function sendMessage() {
        $data = [
            'chat_id' => $this->input->post('chat_id'),
            'sender_id' => $this->session->userdata('b_id') ?? $this->session->userdata('s_id'),
            'receiver_id' => $this->input->post('receiver_id'),
            'message' => $this->input->post('message'),
            'is_offer' => $this->input->post('is_offer') ?? 0, // 0 = normal message, 1 = offer
            'offer_id' => $this->input->post('offer_id') ?? NULL
        ];


        if (contains_restricted_content($data['message'])) {
            echo json_encode(['status' => false, 'message' => 'Sharing contact details is not allowed!']);
            return;
        }

        if (!$data['chat_id'] || !$data['sender_id'] || !$data['message']) {
            echo json_encode(['status' => false, 'message' => 'Invalid input']);
            return;
        }

        $message_id = $this->ChatModel->insertMessage($data);
        echo json_encode(['status' => 'success']);
    }

    
    

    public function getChatMessages() {
        $chatId = $this->input->post('chat_id'); 
        if (empty($chatId)) {
            echo json_encode(['error' => 'Chat ID is required']);
            return;
        }
        $data['messages'] = $this->ChatModel->getMessagesByChatId($chatId);
        $data['chatDetails'] = $this->ChatModel->getChatDetails($chatId);
        echo json_encode($data);
    }


    public function markAsRead() {
        $chat_id = $this->input->post('chat_id');
        $user_id = $this->session->userdata('b_id') ?? $this->session->userdata('s_id');
        $this->ChatModel->markMessagesAsRead($chat_id, $user_id);
        echo json_encode(['status' => 'success']);
    }

    public function getUnreadCounts() {
        $user_id = $this->session->userdata('b_id') ?? $this->session->userdata('s_id');
        $unreadCounts = $this->ChatModel->getUnreadMessageCount($user_id);
        echo json_encode($unreadCounts);
    }
    
    
}

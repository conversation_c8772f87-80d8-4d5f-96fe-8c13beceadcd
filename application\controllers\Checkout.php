<?php
defined('BASEPATH') or exit('No direct script access allowed');
class Checkout extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        if (!$this->session->userdata('userid')) {
            redirect('login');
        }
        
    }
    public function index()
    {
        $data = array();
        $plan_id = $this->input->get("plan");
        $user_id = $this->session->userdata("userid");
        $this->db->where('user_id', $user_id);
        $this->db->where('status', 'active');
        $existing_subscription = $this->db->get('subscriptions')->row_array();
        if($existing_subscription){
            $this->session->set_flashdata('sub_error', 'You already have active subscription plan!');
            redirect('subscriptions');
        }
        else{
            if($plan_id == "1"){
                $start_date = date('Y-m-d H:i:s');
                $data = array(
                    'user_id' => $this->session->userdata('userid'),
                    'plan' => $plan_id,
                    'cus_id' =>  null,
                    'payment_method' => null,
                    'price' =>  null,
                    'start_date' => $start_date,
                    'end_date' => null,
                    'is_auto_renewal' => 1,
                    'status' => 'active',
                    'created_at' => date('Y-m-d H:i:s')
                );
                $this->db->insert('subscriptions', $data);
                $this->session->set_flashdata('sub_success', 'You Free subscription plan has been activated!');
                redirect('subscriptions');
            } 
            else{
                $this->load->Template('checkout/index', $data); 
            }
        }
    }
}
?>
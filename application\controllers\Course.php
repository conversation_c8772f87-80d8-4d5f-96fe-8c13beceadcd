<?php
require_once FCPATH . 'vendor/autoload.php';

use Stripe\Stripe;
use Stripe\PaymentIntent;

class Course extends CI_Controller {

    public function __construct() {
        parent::__construct();
        $this->load->model('CourseModel');
        $this->load->model('PromotionModel');
        $this->load->helper('stripe_helper');
        Stripe::setApi<PERSON>ey('***********************************************************************************************************');
    }

    public function index() {
        $search = $this->input->get('search') ? $this->input->get('search'):""; 
        $data['search'] = $search;
        $data['courses'] = $this->CourseModel->get_approved_courses($search);
        $this->load->Template('courses/list', $data);
    }
    


    public function upload_form() {
        $this->check_login();
        $this->load->Template('courses/upload', $data);
    }

    public function upload() {
        $this->check_login();
        $thumbnail = '';
        $extra_files = [];
        $created_by = "";
        $created_type = "";

        if($this->session->userdata('b_id')){
            $created_by = $this->session->userdata('b_id');
            $created_type = "business";
        }
        else{
            $created_by = $this->session->userdata('s_id');
            $created_type = "sales_professional";
        }

        // Upload thumbnail
        if (!empty($_FILES['thumbnail']['name'])) {
            $config['upload_path'] = './assets/course_thumbnail/';
            $config['allowed_types'] = 'jpg|jpeg|png';
            $config['file_name'] = time() . '_' . $_FILES['thumbnail']['name'];
            $this->load->library('upload', $config);
            if ($this->upload->do_upload('thumbnail')) {
                $thumbnail_data = $this->upload->data();
                $thumbnail = 'assets/course_thumbnail/' . $thumbnail_data['file_name'];
            }
        }

        // Upload extra files
        if (!empty($_FILES['extra_files']['name'][0])) {
            $filesCount = count($_FILES['extra_files']['name']);
            for ($i = 0; $i < $filesCount; $i++) {
                $_FILES['file']['name'] = $_FILES['extra_files']['name'][$i];
                $_FILES['file']['type'] = $_FILES['extra_files']['type'][$i];
                $_FILES['file']['tmp_name'] = $_FILES['extra_files']['tmp_name'][$i];
                $_FILES['file']['error'] = $_FILES['extra_files']['error'][$i];
                $_FILES['file']['size'] = $_FILES['extra_files']['size'][$i];

                $config['upload_path'] = './assets/course_files/';
                $config['allowed_types'] = 'pdf|doc|docx|zip';
                $config['file_name'] = time() . '_' . $_FILES['file']['name'];

                $this->load->library('upload', $config);

                if ($this->upload->do_upload('file')) {
                    $fileData = $this->upload->data();
                    $extra_files[] = 'assets/course_files/' . $fileData['file_name'];
                }
            }
        }

        $data = [
            'title' => $this->input->post('title'),
            'description' => $this->input->post('description'),
            'thumbnail' => $thumbnail,
            'intro_video' => $this->input->post('intro_video'),
            'full_video' => $this->input->post('course_video'),
            'duration' => $this->input->post('duration'),
            'level' => $this->input->post('level'),
            'audience' => $this->input->post('target_audience'),
            'language' => $this->input->post('language'),
            'chapters' => $this->input->post('modules'),
            'price' => $this->input->post('price'),
            'discount' => $this->input->post('discount'),
            'terms_agreed' => $this->input->post('terms') ? 1 : 0,
            'extra_files' => json_encode($extra_files),
            'created_at' => date('Y-m-d H:i:s'),
            'status' => 'pending',
            'created_by' => $created_by,
            'created_type' => $created_type
        ];

        $this->CourseModel->insert_course($data);
        redirect('course/my_courses');
    }

    public function edit($id) {
        $this->check_login();
        $data['course'] = $this->CourseModel->get_course_by_id($id);
        $this->load->Template('courses/edit', $data);
    }

    
    public function update($id) {
        $this->check_login();
        $course = $this->CourseModel->get_course_by_id($id);
    
    
        $upload_thumbnail = $_FILES['thumbnail']['name'] ?? '';
        $upload_extra_file = $_FILES['extra_file']['name'] ?? '';
    
        $thumbnail = $course->thumbnail;
        if ($upload_thumbnail) {
            $thumbnail_path = './assets/course_thumbnail/';
            move_uploaded_file($_FILES['thumbnail']['tmp_name'], $thumbnail_path . $upload_thumbnail);
            $thumbnail = $upload_thumbnail;
        }
    
        $extra_file = $course->extra_file;
        if ($upload_extra_file) {
            $extra_path = './assets/course_files/';
            move_uploaded_file($_FILES['extra_file']['tmp_name'], $extra_path . $upload_extra_file);
            $extra_file = $upload_extra_file;
        }
    
        $data = [
            'title' => $this->input->post('title'),
            'description' => $this->input->post('description'),
            'intro_video' => $this->input->post('intro_video'),
            'full_video' => $this->input->post('full_video'),
            'duration' => $this->input->post('duration'),
            'level' => $this->input->post('level'),
            'audience' => $this->input->post('target_audience'),
            'language' => $this->input->post('language'),
            'chapters' => $this->input->post('modules'),
            'price' => $this->input->post('price'),
            'discount' => $this->input->post('discount_price'),
            'terms_accepted' => $this->input->post('terms_accepted') ? 1 : 0,
            'thumbnail' => $thumbnail,
            'extra_file' => $extra_file,
        ];
    
        $this->CourseModel->update_course($id, $data);
        redirect('course/my_courses');
    }
    

    public function my_courses() {
        $this->check_login();
        $id = "";
        $created_type = "";

        if($this->session->userdata('b_id')){
            $id = $this->session->userdata('b_id');
            $created_type = "business";
        }
        else{
            $id = $this->session->userdata('s_id');
            $created_type = "sales_professional";
        }
        $data['courses'] = $this->CourseModel->get_user_courses($id, $created_type);
        $this->load->Template('courses/my_courses', $data);
    }

    

    public function view($id) {
        $this->check_login();
        $data['course'] = $this->CourseModel->get_course_by_id($id);
        $data['promotion_settings'] = $this->PromotionModel->get_all();
        $this->load->Template('courses/view', $data);
    }

    public function details($id) {
        $data['course'] = $this->CourseModel->get_course_by_id($id);
        $this->load->Template('courses/details', $data);
    }

    public function enroll($course_id) {
        $this->check_login();
        $id = "";
        $created_type = "";

        if($this->session->userdata('b_id')){
            $id = $this->session->userdata('b_id');
            $created_type = "business";
        }
        else{
            $id = $this->session->userdata('s_id');
            $created_type = "sales_professional";
        }
        $exists = $this->CourseModel->check_enrollment($id, $course_id, $created_type);

        if (!$exists) {
            $this->CourseModel->enroll_user($id, $course_id, $created_type);
        }

        redirect('course/my_courses');
    }

    public function enrollment_history()
{
    $this->check_login();
    $user_id = $this->session->userdata('s_id') ?: $this->session->userdata('b_id');
    $type = $this->session->userdata('s_id') ? 'sales_professional' : 'business';

    $data['enrollments'] = $this->CourseModel->get_enrollment_history($user_id, $type);
    $this->load->Template('courses/enrollment_history', $data);
}

public function course_enrollments($course_id)
{
    $this->check_login();
    $data['enrollments'] = $this->CourseModel->get_course_enrollments($course_id);
    $this->load->Template('courses/course_enrollments', $data);
}


    private function check_login()
{
    if (!$this->session->userdata('b_id') && !$this->session->userdata('s_id')) {
        redirect('login');
    }
}

public function paid_enroll() {
    $this->check_login();

    $user_type = $this->session->userdata('user_type');
    $user_id = $user_type === 'business' ? $this->session->userdata('b_id') : $this->session->userdata('s_id');
    $course_id = $this->input->post('course_id');
    $price = $this->input->post('price');
    $payment_method_id = $this->input->post('payment_method_id');

    // Get course info
    $course = $this->db->get_where('courses', ['id' => $course_id])->row();
    $author_id = $course->created_by;
    $author_type = $course->created_type;

    // Determine author table
    $author_table = $author_type === 'business' ? 'businesses' : 'sales_professionals';

    // Get commission percentage
    $commission_setting = $this->db->get_where('commissions', ['id' => '1'])->row();
    $commission_percent = $commission_setting ? floatval($commission_setting->percentage) : 0.00;

    // Payment calculations
    $amount_cents = intval($price * 100);
    $platform_fee = intval($amount_cents * ($commission_percent / 100));
    $author_amount = ($amount_cents - $platform_fee) / 100; // Convert back to dollars for DB

    // Get customer info
    $user_table = $user_type === 'business' ? 'businesses' : 'sales_professionals';
    $user_data = $this->db->get_where($user_table, ['id' => $user_id])->row();

    // Create Stripe customer if not exists
    if (empty($user_data->stripe_customer_id)) {
        $stripe_customer_id = create_stripe_customer($user_id);
        $this->db->where('id', $user_id)->update($user_table, ['stripe_customer_id' => $stripe_customer_id]);
    } else {
        $stripe_customer_id = $user_data->stripe_customer_id;
    }

    try {
        $paymentIntent = \Stripe\PaymentIntent::create([
            'amount' => $amount_cents,
            'currency' => 'usd',
            'payment_method' => $payment_method_id,
            'confirmation_method' => 'manual',
            'confirm' => true,
            'customer' => $stripe_customer_id,
        ]);

        if ($paymentIntent->status === 'requires_action') {
            echo json_encode([
                'requires_action' => true,
                'payment_intent_client_secret' => $paymentIntent->client_secret
            ]);
        } else {
            // Enroll user if not already enrolled
            $exists = $this->CourseModel->check_enrollment($user_id, $course_id, $user_type);
            if (!$exists) {
                $this->CourseModel->enroll_user($user_id, $course_id, $user_type);
            }
            update_user_balance($author_id, $author_table, $author_amount);

            echo json_encode(['success' => true]);
        }
    } catch (\Exception $e) {
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
}



}
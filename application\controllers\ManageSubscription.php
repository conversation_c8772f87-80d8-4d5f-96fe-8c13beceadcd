<?php
defined('BASEPATH') or exit('No direct script access allowed');
class ManageSubscription extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        if (!$this->session->userdata('userid')) {
            redirect('login');
        }
        $this->load->model('SubscriptionModel');
        
    }
    public function index()
    {
        $data = array();
        $id = $this->session->userdata('userid');
        $data['subscriptions'] = $this->SubscriptionModel->getByUserId($id); 
        $this->load->Template('manage_subscriptions/index', $data);  
    }

}
?>
<?php
defined('BASEPATH') OR exit('No direct script access allowed');

require_once FCPATH . 'vendor/autoload.php'; 

use Stripe\Stripe;
use Stripe\Customer;
use Stripe\PaymentIntent;

class Offer extends CI_Controller {

    public function __construct() {
        parent::__construct();
        $this->load->model('OfferModel');
        Stripe::setApiKey('***********************************************************************************************************');
    }

    public function create_offer() {
        $payment_type = $this->input->post('payment_type');

        $data = [
            'chat_id' => $this->input->post('chat_id'),
            'sender_id' => $this->input->post('sender_id'),
            'receiver_id' => $this->input->post('receiver_id'),
            'title' => $this->input->post('title'),
            'description' => $this->input->post('description'),
            'revisions' => $this->input->post('revisions'),
            'delivery_time' => $this->input->post('delivery_time'),
            'payment_type' => $payment_type,
            'status' => 'pending'
        ];

        // Conditional fields
        if ($payment_type === 'hourly') {
            $data['hourly_rate'] = $this->input->post('hourly_rate');
        } elseif ($payment_type === 'hourly_bonus') {
            $data['hourly_rate'] = $this->input->post('hourly_rate');
            $data['bonus'] = $this->input->post('bonus');
        } elseif ($payment_type === 'result_based') {
            $data['result_based'] = $this->input->post('result_based');
        }

        if (!$data['chat_id'] || !$data['sender_id'] || !$data['receiver_id'] || !$data['payment_type']) {
            echo json_encode(['status' => false, 'message' => 'Missing required fields']);
            return;
        }

        $offer_id = $this->OfferModel->create_offer($data);
        echo json_encode(['status' => true, 'offer_id' => $offer_id]);
    }

     // Process the payment
     public function process_payment() {
        $bid_id = $this->input->post("offer_id");
        $user_id = $this->input->post("user_id");
        $amount = $this->input->post("amount");
        $payment_method_id = $this->input->post("payment_method_id");

        $bid = $this->db->get_where("offers", ["id" => $bid_id])->row();
        if (!$bid) {
            echo json_encode(["status" => "error", "message" => "Invalid Offer"]);
            return;
        }

        if ($bid->status == "completed") {
            echo json_encode(["status" => "error", "message" => "Offer already accepted!"]);
            return;
        }
        $this->session->set_userdata('offer_data', $bid);
        

        try {
            // Create a customer in Stripe
            $paymentIntent = \Stripe\PaymentIntent::create([
                "amount" => $amount * 100,  // Convert to cents
                "currency" => "usd",
                "payment_method" => $payment_method_id,
                'setup_future_usage' => 'off_session',
                'confirmation_method' => 'manual', // Manual confirmation to handle SCA
                'confirm' => true,
                'return_url' => base_url('orders/confirm_payment') // Redirect URL after authentication
            ]);

            
            if ($paymentIntent->status === 'requires_action' || $paymentIntent->status === 'requires_source_action') {
               
                echo json_encode([
                    'success' => false,
                    'requires_action' => true,
                    'payment_intent_client_secret' => $paymentIntent->client_secret
                ]);
            } 
            elseif ($paymentIntent->status == "succeeded") {
                $days = $bid->delivery_time;
                // Insert Order
                $this->db->insert("orders", [
                    "business_id" => $this->session->userdata("b_id"),
                    "freelancer_id" => $user_id,
                    "offer_id" => $bid->id,
                    "is_custom_order" => 1,
                    "amount" => $amount,
                    "deadline" => date('Y-m-d', strtotime("+$days days")),
                    "status" => "in-progress",
                    "created_at" => date("Y-m-d H:i:s")
                ]);
    
                // Update Bid Status
                //$this->db->update("bids", ["status" => "accepted"], ["id" => $bid_id]);
    
                // Update Assignment Status
                $this->db->update("offers", ["status" => "accepted"], ["id" => $bid->id]);
    
                echo json_encode(["success" => true]);
            } else {
                echo json_encode(["success" => false, "message" => "Payment Failed"]);
            }

        } catch (\Stripe\Exception\CardException $e) {
            echo json_encode(['success' => false, 'error' => $e->getError()->message]);
        }
    }

    // Confirm payment after additional authentication (if required)
    public function confirm_payment() {
        // Handle the post-authentication process here, if necessary
        $paymentIntentId = $this->input->post('payment_intent_id');

        try {
            $paymentIntent = PaymentIntent::retrieve($paymentIntentId);
            if ($paymentIntent->status === 'succeeded') {
                $bid = $this->session->userdata('offer_data');
                $days = $bid->delivery_time;
                $this->db->insert("orders", [
                    "business_id" => $this->session->userdata("b_id"),
                    "freelancer_id" => $bid->sender_id,
                    "offer_id" => $bid->id,
                    "is_custom_order" => 1,
                    "amount" => $bid->budget,
                    "deadline" => date('Y-m-d', strtotime("+$days days")),
                    "status" => "in-progress",
                    "created_at" => date("Y-m-d H:i:s")
                ]);

                $this->db->update("offers", ["status" => "accepted"], ["id" => $bid->id]);
                $this->session->unset_userdata('bid_data');
                echo json_encode(['success' => true]);
            } else {
                echo json_encode(['success' => false, 'error' => 'Payment failed after authentication']);
            }
        } catch (\Exception $e) {
            echo json_encode(['success' => false, 'error' => $e->getMessage()]);
        }
    }


    public function accept_offer() {
    $offer_id = $this->input->post('offer_id');
    $user_id = $this->input->post("user_id");

    $offer = $this->db->get_where("offers", ["id" => $offer_id])->row();
    if (!$offer) {
        echo json_encode(["status" => false, "message" => "Invalid Offer"]);
        return;
    }

    if ($offer->status == "accepted") {
        echo json_encode(["status" => false, "message" => "Offer already accepted"]);
        return;
    }

     $days = $offer->delivery_time;
                $this->db->insert("orders", [
                    "business_id" => $this->session->userdata("b_id"),
                    "freelancer_id" => $user_id,
                    "offer_id" => $offer->id,
                    "is_custom_order" => 1,
                    "amount" => NULL,
                    "deadline" => date('Y-m-d', strtotime("+$days days")),
                    "status" => "in-progress",
                    "created_at" => date("Y-m-d H:i:s")
                ]);

    $this->db->update("offers", ["status" => "accepted"], ["id" => $offer->id]);
    echo json_encode(["status" => true, "message" => "Offer accepted"]);
}

}

<?php
defined('BASEPATH') OR exit('No direct script access allowed');

require_once FCPATH . 'vendor/autoload.php'; 

use Stripe\Stripe;
use Stripe\Customer;
use Stripe\PaymentIntent;

class Orders extends CI_Controller {

    public function __construct() {
        parent::__construct();
        Stripe::setApiKey('***********************************************************************************************************');
    }

    public function process_payment() {
        $bid_id = $this->input->post("bid_id");
        $user_id = $this->input->post("user_id");
        $amount = $this->input->post("amount");
        $service_fee = $amount * 0.025;
        $total_amount = $amount + $service_fee;
        $payment_method_id = $this->input->post("payment_method_id");

        $bid = $this->db->get_where("bids", ["id" => $bid_id])->row();
        $this->session->set_userdata('bid_data', $bid);
        if (!$bid) {
            echo json_encode(["status" => "error", "message" => "Invalid Bid"]);
            return;
        }

        try {
            $paymentIntent = \Stripe\PaymentIntent::create([
                "amount" => $total_amount * 100,
                "currency" => "usd",
                "payment_method" => $payment_method_id,
                'setup_future_usage' => 'off_session',
                'confirmation_method' => 'manual',
                'confirm' => true,
                'return_url' => base_url('orders/confirm_payment')
            ]);

            if ($paymentIntent->status === 'requires_action' || $paymentIntent->status === 'requires_source_action') {
                echo json_encode([
                    'success' => false,
                    'requires_action' => true,
                    'payment_intent_client_secret' => $paymentIntent->client_secret
                ]);
            } elseif ($paymentIntent->status == "succeeded") {
                $days = $bid->deadline;
                $this->db->insert("orders", [
                    "business_id" => $this->session->userdata("b_id"),
                    "freelancer_id" => $user_id,
                    "assignment_id" => $bid->assignment_id,
                    "bid_id" => $bid_id,
                    "amount" => $amount,
                    "deadline" => date('Y-m-d', strtotime("+$days days")),
                    "status" => "in-progress",
                    "created_at" => date("Y-m-d H:i:s")
                ]);

                $this->db->update("assignments", ["status" => "in-progress"], ["id" => $bid->assignment_id]);
                echo json_encode(["success" => true]);
            } else {
                echo json_encode(["success" => false, "message" => "Payment Failed"]);
            }

        } catch (\Stripe\Exception\CardException $e) {
            echo json_encode(['success' => false, 'error' => $e->getError()->message]);
        }
    }

    public function confirm_payment() {
        $paymentIntentId = $this->input->post('payment_intent_id');

        try {
            $paymentIntent = PaymentIntent::retrieve($paymentIntentId);
            if ($paymentIntent->status === 'succeeded') {
                $bid = $this->session->userdata('bid_data');
                $days = $bid->deadline;
                $this->db->insert("orders", [
                    "business_id" => $this->session->userdata("b_id"),
                    "freelancer_id" => $bid->sales_professional_id,
                    "assignment_id" => $bid->assignment_id,
                    "bid_id" => $bid->id,
                    "amount" => $bid->amount,
                    "deadline" => date('Y-m-d', strtotime("+$days days")),
                    "status" => "in-progress",
                    "created_at" => date("Y-m-d H:i:s")
                ]);

                $this->db->update("assignments", ["status" => "in-progress"], ["id" => $bid->assignment_id]);
                $this->session->unset_userdata('bid_data');
                echo json_encode(['success' => true]);
            } else {
                echo json_encode(['success' => false, 'error' => 'Payment failed after authentication']);
            }
        } catch (\Exception $e) {
            echo json_encode(['success' => false, 'error' => $e->getMessage()]);
        }
    }

    // Accept a result-based bid (no upfront payment)
    public function accept_result_based() {
        $bid_id = $this->input->post("bid_id");
        $user_id = $this->input->post("user_id");

        $bid = $this->db->get_where("bids", ["id" => $bid_id])->row();
        if (!$bid) {
            echo json_encode(["success" => false, "message" => "Invalid Bid"]);
            return;
        }

        $days = $bid->deadline;
        $this->db->insert("orders", [
            "business_id" => $this->session->userdata("b_id"),
            "freelancer_id" => $user_id,
            "assignment_id" => $bid->assignment_id,
            "bid_id" => $bid_id,
            "amount" => NULL,
            "deadline" => date('Y-m-d', strtotime("+$days days")),
            "status" => "in-progress",
            "created_at" => date("Y-m-d H:i:s")
        ]);

        $this->db->update("assignments", ["status" => "in-progress"], ["id" => $bid->assignment_id]);

        echo json_encode(["success" => true, "message" => "Order created as result-based"]);
    }
}

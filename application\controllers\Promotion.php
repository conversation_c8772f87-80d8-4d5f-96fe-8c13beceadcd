<?php
require_once FCPATH . 'vendor/autoload.php';

use Stripe\Stripe;
use Stripe\PaymentIntent;

class Promotion extends CI_Controller {
    

    public function __construct() {
        parent::__construct();
        $this->load->model('PromotionSettingsModel');
        $this->load->model('PromotionModel');
        Stripe::setApiKey('***********************************************************************************************************');
    }

    public function order() {
        $setting_id = $this->input->post('setting_id');
        $promotion_type = $this->input->post('promotion_type');
        $reference_id = $this->input->post('reference_id');
        $payment_method_id = $this->input->post('payment_method_id');

        $setting = $this->db->get_where('promotion_settings', ['id' => $setting_id])->row();
        if (!$setting) {
            echo json_encode(['success' => false, 'message' => 'Invalid Promotion Package']);
            return;
        }

        $total_amount = $setting->price;
        $this->session->set_userdata('promotion_data', [
            'setting_id' => $setting_id,
            'promotion_type' => $promotion_type,
            'reference_id' => $reference_id,
            'amount' => $total_amount
        ]);

        try {
            $paymentIntent = PaymentIntent::create([
                'amount' => $total_amount * 100,
                'currency' => 'usd',
                'payment_method' => $payment_method_id,
                'setup_future_usage' => 'off_session',
                'confirmation_method' => 'manual',
                'confirm' => true,
                'return_url' => base_url('promotion/confirm_payment')
            ]);

            if ($paymentIntent->status === 'requires_action') {
                echo json_encode([
                    'success' => false,
                    'requires_action' => true,
                    'payment_intent_client_secret' => $paymentIntent->client_secret
                ]);
            } elseif ($paymentIntent->status === 'succeeded') {
                $this->_savePromotionOrder();
                echo json_encode(['success' => true]);
            } else {
                echo json_encode(['success' => false, 'message' => 'Payment Failed']);
            }

        } catch (\Exception $e) {
            echo json_encode(['success' => false, 'message' => $e->getMessage()]);
        }
    }

    public function confirm_payment() {
        $paymentIntentId = $this->input->post('payment_intent_id');
        try {
            $paymentIntent = PaymentIntent::retrieve($paymentIntentId);
            if ($paymentIntent->status === 'succeeded') {
                $this->_savePromotionOrder();
                echo json_encode(['success' => true]);
            } else {
                echo json_encode(['success' => false, 'error' => 'Payment failed after authentication']);
            }
        } catch (\Exception $e) {
            echo json_encode(['success' => false, 'error' => $e->getMessage()]);
        }
    }

    private function _savePromotionOrder() {
        $data = $this->session->userdata('promotion_data');
        if (!$data) return;

        $this->db->insert('promotions', [
            'promotion_type' => $data['promotion_type'],
            'reference_id' => $data['reference_id'],
            'setting_id' => $data['setting_id'],
            'price' => $data['amount'],
            'is_active' => '1',
            'start_date' => date('Y-m-d'),
            'end_date' => date('Y-m-d', strtotime('+' . $this->db->get_where('promotion_settings', ['id' => $data['setting_id']])->row()->duration_days . ' days')),
            'created_at' => date('Y-m-d H:i:s')
        ]);

        $this->session->unset_userdata('promotion_data');
    }
}

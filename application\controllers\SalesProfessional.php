<?php
defined('BASEPATH') or exit('No direct script access allowed');
class SalesProfessional extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();
        if (!$this->session->userdata('s_id')) {
            redirect('login');
        }
        $this->load->model('SalesProfessionalModel');
        $this->load->model('PromotionModel');
        $this->load->helper('stripe_helper');
        
    }
    
    public function profile()
    {
        $data = array();
        $data['sales_professional'] = $this->SalesProfessionalModel->get_sales_by_id($this->session->userdata('s_id'));
        $data['promotion_settings'] = $this->PromotionModel->get_all();
        $this->load->Template('sales_professionals/profile', $data);  
    }

    public function editProfile() {
        
        $data = array(
            'full_name' => $this->input->post('full_name'),
            'location' => $this->input->post('location'),
            'industry' => $this->input->post('industry'),
            'experience' => $this->input->post('experience'),
            'specialties' => $this->input->post('specialties'),
            'achievements' => $this->input->post('achievements'),
            'portfolio' => $this->input->post('portfolio'),
            'kvk_vat' => $this->input->post('kvk_vat')
        );
    
        if (!empty($_FILES['profile_photo']['name'])) {
            $config['upload_path'] = './assets/img/sales/';
            $config['allowed_types'] = 'gif|jpg|png';
            $config['max_size'] = 2048;

            $this->load->library('upload', $config);

            if (!$this->upload->do_upload('profile_photo')) {
                $error = ['error' => $this->upload->display_errors()];
                $this->session->set_flashdata('update_error', $error);
            } else {
                $data['profile_picture'] = $this->upload->data('file_name');
            }
        }


        if ($this->SalesProfessionalModel->update_sales($this->session->userdata('s_id'), $data)) {
            $this->session->set_flashdata('update_success', 'Profile updated successfully.');
        } else {
            $this->session->set_flashdata('update_error', 'Failed to update profile. Please try again.');
        }
        redirect('sales/profile');
    }


    public function bid_history() {
        $sales_professional_id = $this->session->userdata('s_id');
        $data['bids'] = $this->SalesProfessionalModel->get_bids_by_sales_professional($sales_professional_id);
        $this->load->Template('sales_professionals/bid_history', $data);
    }

    public function orders() {
        
        $data['in_progress'] = $this->SalesProfessionalModel->get_orders('in-progress');
        $data['completed'] = $this->SalesProfessionalModel->get_orders('completed');
        $data['cancelled'] = $this->SalesProfessionalModel->get_orders('cancelled');
        $this->load->Template('sales_professionals/orders/index', $data);
    }


    public function upload_delivery() {
        $order_id = $this->input->post('order_id');
        $upload_path = './assets/deliveries/';
        if (!is_dir($upload_path)) {
            mkdir($upload_path, 0777, true);
        }
    
        $config['upload_path'] = $upload_path;
        $config['allowed_types'] = 'pdf|doc|docx|zip|rar|png|jpg|webp';
        $config['file_name'] = 'delivery_' . time();
        
        $this->load->library('upload', $config);
    
        if ($this->upload->do_upload('delivery_file')) {
            $file_data = $this->upload->data();
            $data['delivery'] = $file_data['file_name'];
            $this->db->where('id', $order_id);
            $this->db->update('orders', $data);
    
            echo json_encode(['status' => 'success']);
        } else {
            echo json_encode(['status' => 'error', 'message' => $this->upload->display_errors()]);
        }
    }


    public function add_bank_account() {
        $user_id = $this->session->userdata('s_id');
    
        $account_holder_name = $this->input->post('bank_name');
        $routing_number = $this->input->post('routing_number');
        $account_number = $this->input->post('account_number');
    
        // Get or Create Stripe Customer ID
        $stripe_customer_id = $this->db->get_where("sales_professionals", ["id" =>  $user_id])->row()->stripe_customer_id;
        if (!$stripe_customer_id) {
            $stripe_customer_id = create_stripe_customer($user_id);
            $this->db->set('stripe_customer_id', $stripe_customer_id);
            $this->db->where('id', $user_id);
            $this->db->update('sales_professionals');
        }
    
        // Attach and Verify Bank Account
        $result = attach_bank_account($stripe_customer_id, $account_holder_name, $routing_number, $account_number);
        
        if ($result['success']) {
            $this->db->insert('user_bank_accounts', [
                'user_id' => $user_id,
                'stripe_bank_account_id' => $bank_data['bank_account_id'],
                'account_holder_name' => $bank_data['account_holder_name'],
                'last4' => $bank_data['last4'],
                'routing_number' => $bank_data['routing_number'],
                'bank_name' => $bank_data['bank_name'],
            ]);
            echo json_encode(['success' => true, 'message' => 'Bank account added successfully.']);
        } else {
            echo json_encode(['success' => false, 'message' => $result['message']]);
        }
    }
    


    public function withdraw() {
        $user_id = $this->session->userdata('s_id');
        $amount = $this->input->post('amount');
        $bank_account = $this->input->post('bank_account_id');

        $sales = $this->db->get_where("sales_professionals", ["id" =>  $user_id])->row();
        $stripe_account_id = $sales->stripe_customer_id;
        if (!$stripe_account_id) {
            echo json_encode(['success' => false, 'message' => 'User does not have a Stripe account.']);
            return;
        }
    
    
        // Perform Payout using Stripe
        $result = create_payout($stripe_account_id, $bank_account, $amount);
    
        if ($result['success']) {
            echo json_encode(['success' => true, 'message' => 'Withdrawal successful!']);
        } else {
            echo json_encode(['success' => false, 'message' => 'Withdrawal failed.']);
        }
    }

    public function bank_accounts() {
        $user_id = $this->session->userdata('s_id');
        $data['bank_accounts'] = $this->SalesProfessionalModel->get_user_bank_accounts($user_id);
        $this->load->Template('sales_professionals/withdraw', $data);
    }


    public function earnings()
{
    $id = $this->session->userdata('s_id');
    $data['freelancer_earnings'] = $this->db->select('fre.*, sp.full_name as referred_name')
        ->from('freelancer_referral_earnings fre')
        ->join('sales_professionals sp', 'fre.referred_freelancer_id = sp.id')
        ->where('fre.referrer_id', $id)
        ->get()
        ->result();

    // Earnings from referred businesses
    $data['business_earnings'] = $this->db->select('bre.*, b.business_name as business_name')
        ->from('company_referral_earnings bre')
        ->join('businesses b', 'bre.referred_company_id = b.id')
        ->where('bre.referrer_id', $id)
        ->get()
        ->result();

    $this->load->Template('sales_professionals/earnings', $data);
}

    
    
    

}
?>
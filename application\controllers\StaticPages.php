<?php
defined('BASEPATH') or exit('No direct script access allowed');
class StaticPages extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        $this->load->library('email'); 
        $this->load->model('SalesProfessionalModel');
        $this->load->model('BusinessModel');
        $this->load->model('CourseModel');
        $this->load->model('AssignmentModel');
    }
    public function index()
    {
        $data = array();
        $data['courses'] = $this->CourseModel->get_approved_courses_for_home();
        $data['assignments'] = $this->AssignmentModel->get_assignments_for_home();
        $this->load->Template('home/index', $data);  
    }

    public function aboutUs()
    {
        $data = array();
        $this->load->Template('static_pages/about-us', $data);  
    }

     public function referrel_program_explain()
    {
        $data = array();
        $this->load->Template('static_pages/referrel_program_explanation', $data);  
    }


     public function profile_promotion_explain()
    {
        $data = array();
        $this->load->Template('static_pages/profile_promotion', $data);  
    }


    public function contactUs()
    {
        $data = array();
        $this->load->Template('static_pages/contact-us', $data);  
    }

    public function termAndCondition()
    {
        $data = array();
        $this->load->Template('static_pages/term-and-condition', $data);  
    }

    public function privacyPolicy()
    {
        $data = array();
        $this->load->Template('static_pages/privacy-policy', $data);  
    }

    public function subscriptions()
    {
        $data = array();
        $this->load->Template('static_pages/subscriptions', $data);  
    }

    public function sendEmail() {
        
        $name = $this->input->post('name');
        $email = $this->input->post('email');
        $subject = $this->input->post('subject');
        $message = $this->input->post('message');
        $token = $this->input->post('g-recaptcha-response');
        
        if($token == ""){
            $captcha_status = $this->validate_captcha($token);
            if($captcha_status == "success"){
            
                $config = [
                    'protocol' => 'smtp',
                    'smtp_host' => 'mail.dealclosedpartner.nl',
                    'smtp_port' => 465,
                    'smtp_user' => '<EMAIL>',
                    'smtp_pass' => '1cL]9c^Ess!E',
                    'mailtype' => 'html',
                    'charset' => 'utf-8',
                    'wordwrap' => TRUE
                ];
            
                $this->email->initialize($config);
                // Compose email
                $this->load->library('email', $config);
                $this->email->from($email, $name);
                $this->email->to('<EMAIL>'); // Replace with your email
                $this->email->subject($subject);
                $this->email->message($message);

                // Send email and handle the result
                if ($this->email->send()) {
                    $this->session->set_flashdata('success-message', 'Email sent successfully!');
                    redirect('contact-us');
                } else {
                    $this->session->set_flashdata('error-message', 'Failed to send email. Please try again later.');
                    redirect('contact-us');
                }
            }
            else{
                $this->session->set_flashdata('error-message', 'Unablr to validate captcha. Please try Again!.');
                redirect('contact-us');
            }
        }
        else{
            $this->session->set_flashdata('error-message', 'Please Check Captcha!');
                redirect('contact-us');
        }
    }


    public function exploreFreelancers(){
        $data['freelancers'] = $this->SalesProfessionalModel->get_sales_professionals();
        $this->load->Template('static_pages/explore_freelancers', $data);  
    }

    public function search_sales_professionals()
    {
        $query = $this->input->post('query');
        $industry = $this->input->post('industry');
        $location = $this->input->post('location');
    
        $results = $this->SalesProfessionalModel->search_sales_professionals($query, $industry, $location);
    
        $output = '';
        foreach ($results as $professional) {
            $profile_picture = !empty($professional->profile_picture) ? $professional->profile_picture : 'default.png';
            $desc = substr($professional->experience, 0, 120);
            
            // Format the rating to one decimal place
            $average_rating = number_format($professional->average_rating, 1);
            
            // If there are no reviews, show 0
            $review_count = $professional->review_count > 0 ? $professional->review_count : 0;
            
            $output .= '
            <div class="col-md-6">
                <div class="card">
                    <div class="row no-gutters">
                        <div class="col-sm-5">
                            <img class="card-img" src="' . base_url("assets/img/sales/" . $profile_picture) . '" alt="Profile Picture">
                        </div>
                        <div class="col-sm-7">
                            <div class="card-body">
                                <p class="mb-0 mb-2">
                                    <i class="fa fa-star text-warning" aria-hidden="true"></i> 
                                    ' . $average_rating . ' (' . $review_count . ')
                                </p>
                                <p class="card-text">'. $desc .'</p>
                                <a href="' . base_url('freelancer-details/' . $professional->id) . '" class="btn btn-sm view-profile-button">
                                    <i class="fa fa-user" aria-hidden="true"></i> View Profile
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>';
        }
        
    
        echo $output;
    }

    public function freelancerDetails($id){
        $data = array();
        $data['sales_professional'] = $this->SalesProfessionalModel->get_sales_by_id($id);
        $data['reviews'] = $this->SalesProfessionalModel->get_reviews_by_sales_id($id); 
        $this->load->Template('static_pages/freelancer_details', $data);  
    }
    



    public function findBusiness(){
        $data['bussiness'] = $this->BusinessModel->get_business();
        $this->load->Template('static_pages/find_business', $data);  
    }


    public function search_business()
    {
        $query = $this->input->post('query');
        $industry = $this->input->post('industry');
        $location = $this->input->post('location');
    
        $results = $this->BusinessModel->search_business($query, $industry, $location);
    
        $output = '';
        foreach ($results as $professional) {
            $profile_picture = !empty($professional->profile_picture) ? $professional->profile_picture : 'default.png';
            $desc = substr($professional->goals, 0, 120);
            $name= $professional->business_name;
            $output .= '
            <div class="col-md-6">
                 <div class="card">
            <div class="row no-gutters">
                <div class="col-sm-5">
                     <img class="card-img" src="' . base_url("assets/img/business/" . $profile_picture) . '" alt="Profile Picture">
                </div>
                <div class="col-sm-7">
                    <div class="card-body">
                      <h4><b>'. $name .'</b></h4>
                      <p class="card-text">'. $desc .'</p>
                        <a href="' . base_url('business-details/' . $professional->id) . '" class="btn btn-sm view-profile-button"><i class="fa fa-user" aria-hidden="true"></i> View Profile</a>
                    </div>
                </div>
            </div>
        </div>
            </div>';

            
        }
    
        echo $output;
    }

    public function businessDetails($id){
        $data = array();
        $data['business'] = $this->BusinessModel->get_business_by_id($id);
        $this->load->Template('static_pages/business_details', $data);  
    }


    public function leaderboard()
    {
        $data = array();

        // Default filter (Yearly)
        $filter = $this->input->get('filter') ?: 'yearly';
        $data['top_sales'] = $this->SalesProfessionalModel->get_top_sales($filter);
        $this->load->Template('static_pages/leaderboard', $data);  
    }

    public function get_leaderboard_data()
    {
        $filter = $this->input->post('filter');
        $top_sales = $this->SalesProfessionalModel->get_top_sales($filter);
        echo json_encode($top_sales);
    }
    



    function validate_captcha($token){
        $status = "";
        if(isset($token) && !empty($token)){
            $secret = '6LdrkbIqAAAAAALkkZr_zY5Q8HzQvpbV6sE-_1wp';
            $response = file_get_contents("https://www.google.com/recaptcha/api/siteverify?secret=".$secret."&response=".$token."&remoteip=".$_SERVER['REMOTE_ADDR']);
            $response = json_decode($response);
            if($response->success == false){
                $status = "error";
            }
            else{
                $status = "success";
            }       
        }
        else{
            $status = "error";
        }
        return $status;
    }
}

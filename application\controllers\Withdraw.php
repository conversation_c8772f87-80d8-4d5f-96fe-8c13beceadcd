<?php
require_once FCPATH . 'vendor/autoload.php';

use Stripe\Stripe;

class Withdraw extends CI_Controller {

     public function __construct()
    {
        parent::__construct();
         if (!$this->session->userdata('b_id') && !$this->session->userdata('s_id')) {
            redirect('login');
        }
        $this->load->model('Withdraw_model');
        Stripe::setApi<PERSON>ey('***********************************************************************************************************');
        
    }

    public function index() {

        $user_id = $this->session->userdata('s_id') ?: $this->session->userdata('b_id');
        $user_type =   $this->session->userdata('s_id') ? "sales_professional":"business";
        $table =   $this->session->userdata('s_id') ? "sales_professionals":"businesses";
        $this->load->model('Withdraw_model');
        $data['balance'] = $this->Withdraw_model->get_balance($user_id, $table);
        $data['bank_accounts'] = $this->Withdraw_model->get_user_bank_accounts($user_id,$user_type);

        $this->load->Template('withdraw/index', $data);
    }

    public function start_onboarding() {
        $user_id = $this->session->userdata('s_id') ?: $this->session->userdata('b_id');
        $table =   $this->session->userdata('s_id') ? "sales_professionals":"businesses";
        $stripe_account_id = $this->Withdraw_model->get_or_create_stripe_account($user_id, $table);
        echo $stripe_account_id;exit;
        $account_link = \Stripe\AccountLink::create([
            'account' => $stripe_account_id,
            'refresh_url' => base_url('withdraw'),
            'return_url' => base_url('withdraw/complete_onboarding'),
            'type' => 'account_onboarding',
        ]);

        redirect($account_link->url);
    }

    public function complete_onboarding() {
        $user_id = $this->session->userdata('s_id') ?: $this->session->userdata('b_id');
        $user_type = $this->session->userdata('s_id') ? "sales_professional" : "business";
        $table = $this->session->userdata('s_id') ? "sales_professionals" : "businesses";
        $stripe_account_id = $this->Withdraw_model->get_or_create_stripe_account($user_id, $table);

        // Save any external bank accounts added during onboarding
        $this->Withdraw_model->sync_bank_accounts($user_id, $user_type, $stripe_account_id);

        $this->session->set_flashdata('success', 'Bank account added successfully.');
        redirect('withdraw');
    }


    public function process_withdrawal() {
        $user_id = $this->session->userdata('s_id') ?: $this->session->userdata('b_id');
        $table =   $this->session->userdata('s_id') ? "sales_professionals":"businesses";
        $amount = $this->input->post('amount');
        $bank_id = $this->input->post('bank_id');

        $result = $this->Withdraw_model->make_payout($user_id, $table, $bank_id, $amount);

        if ($result['success']) {
            $this->session->set_flashdata('success', 'Payout Successful!');
        } else {
            $this->session->set_flashdata('error', $result['message']);
        }

        redirect('withdraw');
    }
}

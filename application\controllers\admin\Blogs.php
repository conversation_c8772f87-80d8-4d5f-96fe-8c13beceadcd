<?php
defined('BASEPATH') or exit('No direct script access allowed');
class Blogs extends CI_Controller
{
    
    
    public function __construct()
    {
        parent::__construct();
        if (!$this->session->userdata('adminid')) {
            redirect('master/login');
        }
        $this->load->model("admin/BlogsModel");
    }

    public function index()
    {
        $data = array();
        $data["list"] = $this->BlogsModel->get();
        $this->load->adminTemplate('blogs/index', $data);
    }

    public function add()
    {
        $data = array();
        $this->load->adminTemplate('blogs/add',$data);
    }


    public function save()
    {
        $data = $this->input->post();

        $route = str_replace(' ', '-', strtolower($data['route']));
        $data['route'] = $route;
        $data['visibility'] = 0;

        $filename = $_FILES["banner"]['name'];
        $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
        $config['file_name'] = $route .'-'. rand(1, 10000) . "." . $extension;
        $config["upload_path"] = './assets/img/blog/';
        $config["allowed_types"] = 'webp'; 

        if ($_FILES['uploaded_file']['type'] === 'application/octet-stream' && pathinfo($_FILES['uploaded_file']['name'], PATHINFO_EXTENSION) === 'webp') {
            $_FILES['uploaded_file']['type'] = 'image/webp';
        }

        $this->load->library("upload", $config);
        $this->upload->initialize($config);
        if (!$this->upload->do_upload('banner')) {
            echo $this->upload->display_errors();
        } else {
            $fd = $this->upload->data();
            $fn = $fd['file_name'];
            $data["banner"] = $fn;
        }

        $this->BlogsModel->insert($data);

        $this->session->set_flashdata('msg', "Blog has been added successfully.");
        $this->session->set_flashdata('msg-type', "success");
        redirect('master/blogs');

    }
    public function updateStatus()
    {
        $get = $this->input->get();
        $data = array("status" => $get["status"]);
        $this->BlogsModel->updateStatus($data, $get["id"]);
        
        $this->session->set_flashdata('msg', "Blog has been updated successfully.");
        $this->session->set_flashdata('msg-type', "success");
        redirect('master/blogs');
    }

    public function updateIsDeletedStatus()
    {
        $get = $this->input->get();
        $data = array("is_deleted" => $get["status"]);
        $this->BlogsModel->updateIsDeletedStatus($data, $get["id"]);
        
        $this->session->set_flashdata('msg', "Blog has been updated successfully.");
        $this->session->set_flashdata('msg-type', "success");
        redirect('master/blogs');
    }

    public function edit()
    {
        $data = [];
        
        $id = $this->input->get("id");
        $data['blog'] = $this->BlogsModel->getBlogById($id);
        // $data["authors"] = $this->BlogsModel->getAuthors();
      
        $this->load->adminTemplate('blogs/edit', $data);
    }

    public function update(){
        $data = [];
        $id = $this->input->post("id");
        $data['title'] = $this->input->post("title");
        $data['desc'] = $this->input->post("desc");
        $data['body'] = $this->input->post("body");
        $route = str_replace(' ', '-', strtolower($this->input->post("route")));
        $data['route'] = $route;
        if(!empty($_FILES["banner"]['name'])){
            $filename = $_FILES["banner"]['name'];
            $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
            $config['file_name'] = $route .'-'. rand(1, 10000) . "." . $extension;
            $config["upload_path"] = './assets/img/blog/';
            $config["allowed_types"] = 'webp'; 
            $config['detect_mime'] = FALSE;
           
            $this->load->library("upload", $config);
            $this->upload->initialize($config);
            if (!$this->upload->do_upload('banner')) {
                echo $this->upload->display_errors();
            } else {
                $fd = $this->upload->data();
                $fn = $fd['file_name'];
                $data["banner"] = $fn;
            }
        }

        $this->BlogsModel->updateBlog($data, $id);

        $this->session->set_flashdata('msg', "Blog has been updated successfully.");
        $this->session->set_flashdata('msg-type', "success");
        redirect('master/blogs');
    }
}

<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Businesses extends CI_Controller {

    public function __construct() {
        parent::__construct();
        if(!$this->session->userdata('adminid'))
        {
        redirect('master/login');
        }
        $this->load->model('admin/BusinessesModel');
    }

    public function index() {
        $data['businesses'] = $this->BusinessesModel->get_all_businesses();
        $this->load->adminTemplate('businesses/index', $data);
    }

}

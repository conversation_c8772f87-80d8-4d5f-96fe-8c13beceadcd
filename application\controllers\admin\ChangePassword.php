<?php
defined('BASEPATH') or exit('No direct script access allowed');
class ChangePassword extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        if (!$this->session->userdata('adminid')) {
            redirect('master/login');
        }
        $this->load->model("admin/LoginModel");
    }
    public function index()
    {
        $data = array();
        $this->load->adminTemplate('changePassword/index', $data);
    }

    public function update(){
        $data = [];
        $id = $this->session->userdata('adminid');
        $password = $this->input->post("password");
        $confirm_password = $this->input->post("confirm_password");
        if($password !== $confirm_password){
            $this->session->set_flashdata('change-password-error', "Password and Confirm Password doesn't match!.");
            redirect('master/change-password');
        }
        else{
           
            $data['password'] = password_hash($password, PASSWORD_DEFAULT);
            $this->LoginModel->change_password($data, $id);
            $this->session->set_flashdata('change-password-success', "Password has been updated successfully.");
            redirect('master/change-password');
        }

        
    }



}
?>
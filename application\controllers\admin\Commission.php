<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Commission extends CI_Controller {

    public function __construct() {
        parent::__construct();
         if(!$this->session->userdata('adminid'))
        {
        redirect('master/login');
        }
        $this->load->model('admin/CommissionModel');
    }

    public function index() {
        $data['commissions'] = $this->CommissionModel->get_all();
        $this->load->adminTemplate('commissions/list', $data);
    }

    public function add() {
        if ($_POST) {
            $data = [
                'title' => $this->input->post('title'),
                'percentage' => $this->input->post('percentage')
            ];
            $this->CommissionModel->insert($data);
            redirect('admin/commission');
        } else {
            $this->load->adminTemplate('commissions/add');
        }
    }

    public function edit($id) {
        $data['commission'] = $this->CommissionModel->get($id);
        if ($_POST) {
            $update_data = [
                'title' => $this->input->post('title'),
                'percentage' => $this->input->post('percentage')
            ];
            $this->CommissionModel->update($id, $update_data);
            redirect('admin/commission');
        } else {
            $this->load->adminTemplate('commissions/edit', $data);
        }
    }

    public function delete($id) {
        $this->CommissionModel->delete($id);
        redirect('admin/commission');
    }
}

<?php
// Controller: CourseController.php
class Courses extends CI_Controller {

    public function __construct() {
        parent::__construct();
        if (!$this->session->userdata('adminid')) {
            redirect('master/login');
        }
        $this->load->model("admin/CoursesModel");
    }

    public function index() {
        $data['courses'] = $this->CoursesModel->get_all_courses();
        $this->load->adminTemplate('courses/index', $data);
    }

    public function update_status() {
        $id = $this->input->post('id');
        $status = $this->input->post('status');
    
        if (in_array($status, ['pending', 'approved', 'disapproved'])) {
            if ($this->CourseModel->update_status($id, $status)) {
                echo json_encode(['status' => true, 'message' => 'Course status updated to ' . ucfirst($status) . '.']);
            } else {
                echo json_encode(['status' => false, 'message' => 'Failed to update course status.']);
            }
        } else {
            echo json_encode(['status' => false, 'message' => 'Invalid status value.']);
        }
    }
    

    

}
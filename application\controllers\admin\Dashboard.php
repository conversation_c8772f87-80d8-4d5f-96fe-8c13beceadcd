<?php
defined('BASEPATH') or exit('No direct script access allowed');
class Dashboard extends CI_Controller{
  public function __construct()
 {
  parent::__construct();
  if(!$this->session->userdata('adminid'))
  {
   redirect('master/login');
  }
  $this->load->model("admin/DashboardModel");
 }
    public function index(){
      $data=array();
      $data=array();
      $data['ordercount']=$this->DashboardModel->totalOrders();
      $data['totalSalesProfessionals']=$this->DashboardModel->totalSalesProfessionals();
      $data["totalBusinesses"] = $this->DashboardModel->totalBusinesses();
      $data["todayorders"] = $this->DashboardModel->todayOrders();
      $data["currentmonthorders"] = $this->DashboardModel->count_current_month_orders();
      $data["previousmonthorders"] = $this->DashboardModel->count_previous_month_orders();
      $monthly_orders = $this->DashboardModel->getMonthlyOrders();
      $monthly_counts = array_fill(1, 12, 0);
      foreach ($monthly_orders as $order) {
          $monthly_counts[(int)$order['month']] = (int)$order['count'];
      }
      $data['graph_data'] = [
        ['y' => 'January', 'a' => $monthly_counts[1]],
        ['y' => 'February', 'a' => $monthly_counts[2]],
        ['y' => 'March', 'a' => $monthly_counts[3]],
        ['y' => 'April', 'a' => $monthly_counts[4]],
        ['y' => 'May', 'a' => $monthly_counts[5]],
        ['y' => 'June', 'a' => $monthly_counts[6]],
        ['y' => 'July', 'a' => $monthly_counts[7]],
        ['y' => 'August', 'a' => $monthly_counts[8]],
        ['y' => 'September', 'a' => $monthly_counts[9]],
        ['y' => 'October', 'a' => $monthly_counts[10]],
        ['y' => 'November', 'a' => $monthly_counts[11]],
        ['y' => 'December', 'a' => $monthly_counts[12]],
    ];

    
		  $this->load->adminTemplate('dashboard/index',$data);
    }
}
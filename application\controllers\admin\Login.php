<?php
defined('BASEPATH') or exit('No direct script access allowed');
class Login extends CI_Controller{
	public function __construct(){
		parent::__construct();
        if($this->session->userdata('adminid'))
        {
            redirect('master/dashboard');
        }
        $this->load->model("admin/loginModel");
        
	}

	public function index(){
        $this->load->view('admin/dashboard/admin-login');
    }

    public function can_login(){
        $username = $this->input->post('username');
        $password = $this->input->post('password');
        $result = $this->loginModel->get_user($username); // Fetch user data by username
    
        if($result){

            $hashed_password = $result->password; // Assuming the password is stored as 'password' in the database
    
            if(password_verify($password, $hashed_password)){
                if ($this->input->post('remember')) {
                    // Set cookies for 6 months (6 * 30 * 24 * 60 * 60 seconds)
                    setcookie('deal_admin_username', $username, time() + (6 * 30 * 24 * 60 * 60), '/');
                    setcookie('deal_admin_password', $password, time() + (6 * 30 * 24 * 60 * 60), '/');
                } else {
                    // Clear cookies if 'remember' is not checked
                    setcookie('deal_admin_username', '', time() - 3600, '/');
                    setcookie('deal_admin_password', '', time() - 3600, '/');
                }
                $this->session->set_userdata('adminid',$result->id);
                $this->session->set_userdata('username',$result->username);
                $this->session->set_flashdata('success','User Login Successfully');
                redirect('master/dashboard');
            } else {
                $this->session->set_flashdata('login-error','Invalid Username or Password');
                redirect('master/login');
            }
        } else {
            $this->session->set_flashdata('login-error','Invalid Username or Password');
            redirect('master/login');
        }
    }

    
    
}
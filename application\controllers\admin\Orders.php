<?php
defined('BASEPATH') or exit('No direct script access allowed');
class Orders extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        if (!$this->session->userdata('adminid')) {
            redirect('master/login');
        }
        $this->load->model("admin/OrdersModel");
    }
    
    public function index()
    {
        $data = array();
        $data["list"] = $this->OrdersModel->get_all();
        $this->load->adminTemplate('orders/index', $data);
    }

   

}
?>
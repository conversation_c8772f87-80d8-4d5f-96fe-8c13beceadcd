<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class PromotionSettings extends CI_Controller {

    public function __construct() {
        parent::__construct();
        if (!$this->session->userdata('adminid')) {
            redirect('master/login');
        }
        $this->load->model('admin/PromotionSettingsModel');
    }

    public function index() {
        $data['settings'] = $this->PromotionSettingsModel->get_all();
        $this->load->adminTemplate('promotion_settings/list', $data);
    }

    public function add() {
        if ($_POST) {
            $this->PromotionSettingsModel->insert([
                'type' => $this->input->post('type'),
                'duration_days' => $this->input->post('duration_days'),
                'price' => $this->input->post('price')
            ]);
            $this->session->set_flashdata('success', 'Promotion setting added.');
            redirect('master/promotion-settings');
        }
        $this->load->adminTemplate('promotion_settings/add');
    }

    public function edit($id) {
        $data['setting'] = $this->PromotionSettingsModel->get($id);
        if ($_POST) {
            $this->PromotionSettingsModel->update($id, [
                'type' => $this->input->post('type'),
                'duration_days' => $this->input->post('duration_days'),
                'price' => $this->input->post('price')
            ]);
            $this->session->set_flashdata('success', 'Promotion setting updated.');
            redirect('master/PromotionSettings');
        }
        $this->load->adminTemplate('promotion_settings/edit', $data);
    }

    public function delete($id) {
        $this->PromotionSettings_model->delete($id);
        $this->session->set_flashdata('success', 'Promotion setting deleted.');
        redirect('master/PromotionSettings');
    }
}

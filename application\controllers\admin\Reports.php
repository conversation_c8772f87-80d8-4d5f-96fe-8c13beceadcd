<?php
defined('BASEPATH') or exit('No direct script access allowed');
class Reports extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        if(!$this->session->userdata('adminid'))
  {
   redirect('master/login');
  }
        $this->load->model("admin/ReportsModel");
    }
    public function index()
    {
        $data = array();
        $vendor = $this->input->post('vendor');
        $branch_type = $this->input->post('branch_type');
        $data['vendors'] = $this->ReportsModel->getVendors();
        $data["list"] = $this->ReportsModel->totalOrders($vendor, $branch_type);
        $this->load->adminTemplate('reports/index', $data);
    }

    



}
?>
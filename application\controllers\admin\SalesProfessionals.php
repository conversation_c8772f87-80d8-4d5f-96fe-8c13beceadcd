<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class SalesProfessionals extends CI_Controller {

    public function __construct() {
        parent::__construct();
        if(!$this->session->userdata('adminid'))
        {
        redirect('master/login');
        }
        $this->load->model('admin/SalesProfessionalsModel');
        $this->load->library('upload');
    }

    public function index() {
        $data['salesProfessionals'] = $this->SalesProfessionalsModel->get_all_sales_professionals();
        $this->load->adminTemplate('sales_professionals/index', $data);
    }

}

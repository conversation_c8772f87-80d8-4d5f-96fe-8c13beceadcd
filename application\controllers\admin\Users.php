<?php
defined('BASEPATH') or exit('No direct script access allowed');
class Users extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        if (!$this->session->userdata('adminid')) {
            redirect('master/login');
        }
        $this->load->model("admin/UsersModel");
    }
    
    public function index()
    {
        $data = array();
        $data["list"] = $this->UsersModel->get();
        $this->load->adminTemplate('users/index', $data);
    }

    public function subscriptions($id)
    {
        $data = array();
        $data["list"] = $this->UsersModel->get_by_id($id);
        $this->load->adminTemplate('users/subscriptions', $data);
    }

}
?>
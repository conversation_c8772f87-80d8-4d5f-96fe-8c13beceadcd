<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Base Controller Class
 * 
 * This class extends CI_Controller to handle dynamic properties
 * for PHP 8.2 compatibility
 */
class MY_Controller extends CI_Controller {
    
    // Common model properties that might be loaded dynamically
    public $SalesProfessionalModel;
    public $BusinessModel;
    public $CourseModel;
    public $AssignmentModel;
    public $PromotionModel;
    public $BlogModel;
    public $ChatModel;
    public $OrderModel;
    public $WithdrawModel;
    public $OfferModel;
    public $UserModel;
    public $AdminModel;
    public $CommissionModel;
    public $ReportModel;
    public $SubscriptionModel;
    public $NotificationModel;
    public $PaymentModel;
    public $ReviewModel;
    public $FavoriteModel;
    public $BidModel;
    public $PortfolioModel;
    public $SecurityModel;
    public $StatisticsModel;
    public $ReferralModel;
    public $PayoutModel;
    public $EnrollmentModel;
    
    // Common library properties
    public $email;
    public $session;
    public $form_validation;
    public $upload;
    public $pagination;
    public $encryption;
    public $user_agent;
    public $calendar;
    public $cart;
    public $ftp;
    public $image_lib;
    public $javascript;
    public $migration;
    public $parser;
    public $table;
    public $trackback;
    public $typography;
    public $unit_test;
    public $xmlrpc;
    public $xmlrpcs;
    public $zip;
    
    // Database property
    public $db;
    
    // Input/Output properties
    public $input;
    public $output;
    public $uri;
    public $router;
    public $config;
    public $lang;
    public $security;
    public $benchmark;
    
    public function __construct() {
        parent::__construct();
    }
}


<?php

    function time_ago($datetime, $full = false) {
        $now = new DateTime;
        $ago = new DateTime($datetime);
        $diff = $now->diff($ago);

        $diff->w = floor($diff->d / 7);
        $diff->d -= $diff->w * 7;

        $string = array(
            'y' => 'year',
            'm' => 'month',
            'w' => 'week',
            'd' => 'day',
            'h' => 'hour',
            'i' => 'minute',
            's' => 'second',
        );
        foreach ($string as $k => &$v) {
            if ($diff->$k) {
                $v = $diff->$k . ' ' . $v . ($diff->$k > 1 ? 's' : '');
            } else {
                unset($string[$k]);
            }
        }

        if (!$full) $string = array_slice($string, 0, 1);
        return $string ? implode(', ', $string) . ' ago' : 'just now';
 }




 function contains_restricted_content($message) {
    $blocked_patterns = [
        '/\b\d{10,15}\b/', // Phone numbers (10-15 digits)
        '/\b(?:facebook|fb|telegram|whatsapp|twitter|snapchat|instagram|linkedin|skype)\b/i', // Social media
        '/(?:@|at)\s?(?:gmail|yahoo|hotmail|outlook|protonmail|aol)\.com/i', // Emails
        '/\b(?:\+\d{1,3}[-.\s]?)?\(?\d{2,4}\)?[-.\s]?\d{3,5}[-.\s]?\d{3,5}\b/' // Phone formats with +, -, ()
    ];

    $allowed_patterns = [
        '/zoom\.us\/j\//i', // Zoom Links
        '/meet\.google\.com/i' // Google Meet Links
    ];

    // Allow Zoom and Google Meet links
    foreach ($allowed_patterns as $allowed) {
        if (preg_match($allowed, $message)) {
            return false;
        }
    }

    // Block restricted content
    foreach ($blocked_patterns as $pattern) {
        if (preg_match($pattern, $message)) {
            return true;
        }
    }

    return false;
}


function get_loggedin_user_balance() {
    $CI =& get_instance();
    $CI->load->database();

    if ($CI->session->userdata('s_id')) {
        $user_id = $CI->session->userdata('s_id');
        $table = 'sales_professionals';
    } elseif ($CI->session->userdata('b_id')) {
        $user_id = $CI->session->userdata('b_id');
        $table = 'businesses';
    } else {
        return 0; // Not logged in
    }

    $CI->db->select('total_balance');
    $CI->db->from($table);
    $CI->db->where('id', $user_id);
    $query = $CI->db->get();

    if ($query->num_rows() > 0) {
        return (float) $query->row()->total_balance;
    }

    return 0;
}



function update_user_balance($user_id, $table, $amount) {
    $CI =& get_instance();
    $CI->load->database();

    // Ensure $amount is numeric
    $amount = floatval($amount);

    // Fetch current balance
    $CI->db->select('total_balance');
    $CI->db->where('id', $user_id);
    $query = $CI->db->get($table);
    $row = $query->row();

    if (!$row) {
        return false; // User not found
    }

    $current_balance = floatval($row->total_balance);
    $new_balance = $current_balance + $amount;

    // Update balance
    $CI->db->where('id', $user_id);
    return $CI->db->update($table, ['total_balance' => $new_balance]);
}

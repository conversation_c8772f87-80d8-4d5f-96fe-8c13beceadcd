<?php
require_once FCPATH . 'vendor/autoload.php'; 

use Stripe\Stripe;
use Stripe\Customer;
use Stripe\BalanceTransaction;
use Stripe\Payout;
use Stripe\Account;

function initialize_stripe() {
    $CI = &get_instance();
    Stripe::setApiKey('***********************************************************************************************************');
}

// Create Stripe Customer
function create_stripe_customer($sales_person_id) {
    initialize_stripe();
    $customer = Customer::create([
        'description' => 'User ID: ' . $sales_person_id
    ]);
    return $customer->id;
}



// Get User Total Balance
function get_user_balance($stripe_customer_id) {
    initialize_stripe();
    $balance = Customer::retrieveBalance($stripe_customer_id);
    return $balance->available[0]->amount / 100; // Convert to dollars
}



function attach_bank_account($stripe_customer_id, $account_holder_name, $routing_number, $account_number) {
    initialize_stripe();
    try {
        // Create a Bank Account Token
        $bank_token = Token::create([
            'bank_account' => [
                'country' => 'US',
                'currency' => 'usd',
                'account_holder_name' => $account_holder_name,
                'account_holder_type' => 'individual',
                'routing_number' => $routing_number,
                'account_number' => $account_number,
            ],
        ]);

        // Verify Bank Account Token
        if (!$bank_token || !$bank_token->id) {
            return ['success' => false, 'message' => 'Invalid bank account details.'];
        }

        // Attach the Bank Account to the Customer
        $bank_account = Customer::createSource(
            $stripe_customer_id,
            ['source' => $bank_token->id]
        );

        // Check for Verification Status
        if ($bank_account->status != 'verified') {
            return ['success' => false, 'message' => 'Bank account could not be verified.'];
        }
        return [
            'success' => true,
            'bank_account_id' => $bank_account->id,
            'last4' => $bank_account->last4,
            'routing_number' => $bank_account->routing_number,
            'bank_name' => $bank_account->bank_name ?? 'Unknown Bank',
        ];
    } catch (\Stripe\Exception\ApiErrorException $e) {
        log_message('error', 'Stripe Bank Account Error: ' . $e->getMessage());
        return ['success' => false, 'message' => $e->getMessage()];
    }
}


function create_payout($stripe_customer_id, $bank_account_id, $amount) {
    initialize_stripe();
    try {
        $payout = Payout::create([
            'amount' => $amount * 100, 
            'currency' => 'usd',
            'method' => 'standard',
            'destination' => $bank_account_id
        ], ['stripe_account' => $stripe_customer_id]);

        return ['success' => true, 'payout_id' => $payout->id];
    } catch (Exception $e) {
        log_message('error', 'Stripe Payout Error: ' . $e->getMessage());
        return false;
    }
}






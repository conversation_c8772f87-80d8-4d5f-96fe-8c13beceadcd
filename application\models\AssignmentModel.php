<?php
class AssignmentModel extends CI_Model {

    public function get_assignments_count($search = null)
{
    $this->db->from('assignments');
    if (!empty($search)) {
        $this->db->like('title', $search);
    }
    if (!$this->session->userdata('s_id')) {
        $this->db->where('is_public', 1);
    }
    return $this->db->count_all_results();
}

    
    // public function get_assignments_with_business_details($limit, $offset) {
    //     $this->db->select('assignments.*, COUNT(bids.id) as bid_count, businesses.business_name, businesses.industry');
    //     $this->db->from('assignments');
    //     $this->db->join('businesses', 'assignments.user_id = businesses.id');
    //     $this->db->join('bids', 'bids.assignment_id = assignments.id', 'left');
    //     if (!$this->session->userdata('s_id')) {
    //         $this->db->where('assignments.is_public', 1);
    //     }
    //     $this->db->group_by('assignments.id');
    //     $this->db->order_by('assignments.created_at', 'DESC');
    //     $this->db->limit($limit, $offset);
    //     $query = $this->db->get();
    
    //     return $query->result();
    // }

    public function get_assignments_with_business_details($limit, $offset, $search = null)
{
    $today = date('Y-m-d');

    $subquery = "(SELECT reference_id, 1 AS is_promoted 
                  FROM promotions 
                  WHERE type = 'company_job' 
                    AND start_date <= '{$today}' 
                    AND end_date >= '{$today}') AS promo";

    $this->db->select('assignments.*, 
                       COUNT(bids.id) AS bid_count, 
                       businesses.business_name, 
                       businesses.industry,
                       COALESCE(promo.is_promoted, 0) AS is_promoted');
    $this->db->from('assignments');
    $this->db->join('businesses', 'assignments.user_id = businesses.id');
    $this->db->join('bids', 'bids.assignment_id = assignments.id', 'left');
    $this->db->join($subquery, 'promo.reference_id = assignments.id', 'left');

    if (!$this->session->userdata('s_id')) {
        $this->db->where('assignments.is_public', 1);
    }

    if (!empty($search)) {
        $this->db->like('assignments.title', $search);
    }

    $this->db->group_by('assignments.id');
    $this->db->order_by('is_promoted', 'DESC');
    $this->db->order_by('assignments.created_at', 'DESC');
    $this->db->limit($limit, $offset);

    $query = $this->db->get();
    return $query->result();
}

    

    public function get_assignments_for_home() {
        $today = date('Y-m-d');
    
        // Subquery for active assignment promotions
        $subquery = "(SELECT reference_id, 1 AS is_promoted 
                      FROM promotions 
                      WHERE type = 'company_job' 
                        AND start_date <= '{$today}' 
                        AND end_date >= '{$today}') AS promo";
    
        $this->db->select('assignments.*, 
                           COUNT(bids.id) AS bid_count, 
                           businesses.business_name, 
                           businesses.industry,
                           COALESCE(promo.is_promoted, 0) AS is_promoted');
        $this->db->from('assignments');
        $this->db->join('businesses', 'assignments.user_id = businesses.id');
        $this->db->join('bids', 'bids.assignment_id = assignments.id', 'left');
        $this->db->join($subquery, 'promo.reference_id = assignments.id', 'left');
    
        if (!$this->session->userdata('s_id')) {
            $this->db->where('assignments.is_public', 1);
        }
    
        $this->db->group_by('assignments.id');
        $this->db->order_by('is_promoted', 'DESC');
        $this->db->order_by('assignments.created_at', 'DESC');
        $this->db->limit(3);
        
        $query = $this->db->get();
        return $query->result();
    }

    public function get_assignment_by_id($assignment_id)
{
    $this->db->select('*');
    $this->db->from('assignments');
    $this->db->where('id', $assignment_id);
    $query = $this->db->get();
    
    if ($query->num_rows() == 1) {
        return $query->row();
    } else {
        return null;
    }
}


public function check_existing_bid($assignment_id, $sales_professional_id) {
    $this->db->from('bids');
    $this->db->where('assignment_id', $assignment_id);
    $this->db->where('sales_professional_id', $sales_professional_id);
    $query = $this->db->get();

    return $query->num_rows() > 0;
}



    public function insert_bid($data) {
        return $this->db->insert('bids', $data);
    }
    
    
}

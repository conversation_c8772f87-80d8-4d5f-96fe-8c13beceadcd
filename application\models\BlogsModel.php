<?php

class BlogsModel extends CI_Model
{
    public function get()
    {
        $result = $this->db->select('*')->where('status', 1)->where('is_deleted', 0)->order_by('id', 'desc')->get("blogs")->result();
        return $result;
    }

    public function countAll() {
        return $this->db->count_all('blogs'); // Replace 'blogs' with your actual table name
    }

    public function getPaginated($limit, $start) {
        $this->db->limit($limit, $start);
        $this->db->order_by('id', 'DESC'); // Sorting by newest first
        $query = $this->db->get('blogs'); // Replace 'blogs' with your actual table name
        return $query->result();
    }

    
    public function getByRoute($route)
    {
        $row = $this->db->select("*")->where('status', 1)->where('is_deleted', 0)->where('route', $route)->get("blogs")->row();
        return $row;
    }

    public function getLatestBlogs($route, $limit){
        $result = $this->db->select("*")->where('status', 1)->where('is_deleted', 0)->where('route !=', $route)->limit($limit)->order_by('id', 'desc')->get("blogs")->result();
        return $result;
    }

    public function getRelatedBlogs(){
        $result = $this->db->select("*")->where('status', 1)->where('is_deleted', 0)->limit(5)->order_by('id', 'desc')->get("blogs")->result();
        return $result;
    }
    
    public function getAuthorById($id)
    {
        $this->db->select("*");
        $this->db->where("ID", $id);
        $query = $this->db->get("authors");
        return $query->row_array();

    }

    public function get_blogs($filters = [])
    {
        $this->db->select("*");
        if (!empty($filters) && $filters != '') {
            $query = $filters;
            $this->db->like('blog_title', $query["search-blog"]);

        }
        //$this->db->like('programme_year',"part-1");
        $query = $this->db->get("blogs");
        return $query->result();

    }

    public function get_blog($route)
    {
        $this->db->select("*");
        $this->db->where('route', $route);
        $query = $this->db->get("blogs");
        return $query->row_array();
    }

    public function getBlogById($id)
    {
        $this->db->select("*");
        $this->db->where("id", $id);
        $query = $this->db->get("blogs");
        return $query->row_array();

    }
 

}

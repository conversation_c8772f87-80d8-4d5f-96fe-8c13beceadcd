<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class BusinessModel extends CI_Model {
    public function __construct() {
        parent::__construct();
    }

    public function get_business_by_id($id) {
        return $this->db->get_where('businesses', ['id' => $id])->row();
    }

    public function update_business($id, $data) {
        $this->db->where('id', $id);
        $this->db->update('businesses', $data);
    }


    public function insert_assignment($data)
    {
        $this->db->insert('assignments', $data);
    }

    public function get_assignment($id, $user_id)
    {
        return $this->db->get_where('assignments', ['id' => $id, 'user_id' => $user_id])->row();
    }

    public function update_assignment($id, $data)
    {
        $this->db->where('id', $id);
        $this->db->update('assignments', $data);
    }


    public function get_assignments_with_bid_count($business_user_id, $limit, $offset) {
        $this->db->select('assignments.*, COUNT(bids.id) as bid_count');
        $this->db->from('assignments');
        $this->db->join('bids', 'bids.assignment_id = assignments.id', 'left');
        $this->db->where('assignments.user_id', $business_user_id);
        $this->db->group_by('assignments.id');
        $this->db->order_by('assignments.created_at', 'DESC');
        $this->db->limit($limit, $offset);
        $query = $this->db->get();
    
        return $query->result();
    }
    
    public function get_count($business_user_id) {
        $this->db->where('user_id', $business_user_id);
        $this->db->from('assignments');
        return $this->db->count_all_results();
    }



    public function get_bids_for_assignment($assignment_id, $limit, $offset) {
        $this->db->select('bids.*, sales_professionals.full_name as sales_professional_name');
        $this->db->from('bids');
        $this->db->join('sales_professionals', 'bids.sales_professional_id = sales_professionals.id');
        $this->db->where('bids.assignment_id', $assignment_id);
        $this->db->limit($limit, $offset);
        $query = $this->db->get();
    
        return $query->result();
    }
    
    public function get_bids_count($assignment_id) {
        $this->db->where('assignment_id', $assignment_id);
        $this->db->from('bids');
        return $this->db->count_all_results();
    }

    public function get_assignment_by_id($assignment_id)
{
    $this->db->select('*');
    $this->db->from('assignments');
    $this->db->where('id', $assignment_id);
    $query = $this->db->get();
    
    if ($query->num_rows() == 1) {
        return $query->row();
    } else {
        return null;
    }
}


public function get_business()
    {
        $query = $this->db->get('businesses');
        return $query->result();
    }

public function search_business($query, $industry, $location)
    {
        $this->db->like('business_name', $query);
        
        if (!empty($industry)) {
            $this->db->where('industry', $industry);
        }
        if (!empty($location)) {
            $this->db->where('location', $location);
        }
    
        $query = $this->db->get('businesses');
        return $query->result();
    }


   



    public function get_orders($status) {
        $business_id = $this->session->userdata('b_id');
        $this->db->select('o.*, 
                          IF(o.is_custom_order = 1, of.title, a.title) AS title,
                           sp.id as sales_professional_id,
                          sp.full_name as sales_professional_name, 
                          sp.profile_picture as sales_professional_image,
                          r.id as review_id');  // Get review ID if it exists
        $this->db->from('orders o');
        $this->db->join('sales_professionals sp', 'o.freelancer_id = sp.id', 'left');
        $this->db->join('offers of', 'o.offer_id = of.id', 'left');
        $this->db->join('assignments a', 'o.assignment_id = a.id', 'left');
        $this->db->join('reviews r', 'o.id = r.order_id', 'left'); // Join with reviews table
        $this->db->where('o.business_id', $business_id);
        $this->db->where('o.status', $status);
        $query = $this->db->get();
        return $query->result();
        
    }
    

    public function mark_as_complete($order_id) {
        $this->db->set('status', 'completed');
        $this->db->where('id', $order_id);
        return $this->db->update('orders');
    }

    public function mark_as_cancel($order_id) {
        $this->db->set('status', 'cancelled');
        $this->db->where('id', $order_id);
        return $this->db->update('orders');
    }
    
    
    
}
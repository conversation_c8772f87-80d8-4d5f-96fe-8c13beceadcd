<?php
class ChatModel extends CI_Model {

    public function get_or_create_chat($business_id, $freelancer_id) {
        $this->db->where("(business_id = $business_id AND freelancer_id = $freelancer_id) 
                          OR (business_id = $freelancer_id AND freelancer_id = $business_id)");
        $query = $this->db->get('chats');

        if ($query->num_rows() > 0) {
            return $query->row()->id;
        }

        // Create new chat
        $data = [
            'business_id' => $business_id,
            'freelancer_id' => $freelancer_id
        ];
        $this->db->insert('chats', $data);
        return $this->db->insert_id();
    }

    public function get_user_chats($user_id, $is_business = true) {
        $this->db->select('c.id AS chat_id, 
                           b.id AS business_id, b.business_name,
                           s.id AS freelancer_id, s.full_name AS freelancer_name');
        $this->db->from('chats c');
        $this->db->join('businesses b', 'c.business_id = b.id', 'left');
        $this->db->join('sales_professionals s', 'c.freelancer_id = s.id', 'left');
        $this->db->where($is_business ? 'c.business_id' : 'c.freelancer_id', $user_id);

        return $this->db->get()->result_array();
    }

    public function get_chat_messages($chat_id) {
        $this->db->where('chat_id', $chat_id);
        $this->db->order_by('created_at', 'ASC');
        return $this->db->get('messages')->result_array();
    }

    public function send_message($data) {
        $this->db->insert('messages', $data);
        return $this->db->insert_id();
    }



    public function check_existing_chat($business_id, $sales_professional_id) {
        $this->db->where('business_id', $business_id);
        $this->db->where('freelancer_id', $sales_professional_id);
        $query = $this->db->get('chats');
        return $query->row(); // Return chat if exists
    }

    public function create_chat($business_id, $sales_professional_id, $message) {
        // Create new chat
        $data = [
            'business_id' => $business_id,
            'freelancer_id' => $sales_professional_id,
            'created_at' => date('Y-m-d H:i:s')
        ];
        $this->db->insert('chats', $data);
        $chat_id = $this->db->insert_id();

        // Insert first message
        $msg_data = [
            'chat_id' => $chat_id,
            'sender_id' => $business_id, // Business is the sender
            'message' => $message
        ];
        $this->db->insert('messages', $msg_data);

        return $chat_id; // Return the new chat ID
    }

    public function getBusinessChat($id){
        $this->db->select('c.*, c.id AS chat_id, s.*, s.id AS sales_id');
        $this->db->from('chats c');
        $this->db->join('sales_professionals s', 'c.freelancer_id = s.id', 'inner');
        $this->db->where('c.business_id', $id);
        return $this->db->get()->result_array();
    }

    public function getSalesChat($id){
        $this->db->select('c.*, c.id AS chat_id, b.*, b.id AS business_id');
        $this->db->from('chats c');
        $this->db->join('businesses b', 'c.business_id = b.id', 'inner');
        $this->db->where('c.freelancer_id', $id);
        return $this->db->get()->result_array();
    }

    public function getMessagesByChatId($chatId) {

        $this->db->select('m.*, m.created_at AS messageTime, b.*, b.id AS businessId, b.profile_picture AS bImage, s.*, s.id AS salesId, s.profile_picture AS sImage, c.*, c.id AS chatId');
        $this->db->from('messages m'); 
        $this->db->join('chats c', 'm.chat_id = c.id', 'inner');
        $this->db->join('businesses b', 'c.business_id = b.id', 'inner');
        $this->db->join('sales_professionals s', 'c.freelancer_id = s.id', 'inner');
        $this->db->where('m.chat_id', $chatId);
        $this->db->order_by('m.created_at', 'ASC'); 

        $query = $this->db->get();

        if ($query->num_rows() > 0) {
            return $query->result_array();
        } else {
            return [];
        }
    }

    public function getChatDetails($chatId){
        $this->db->select('b.*, b.id AS businessId, b.profile_picture AS bImage, s.*, s.id AS salesId, s.profile_picture AS sImage, c.*, c.id AS chatId');
        $this->db->from('chats c'); 
        $this->db->join('businesses b', 'c.business_id = b.id', 'inner');
        $this->db->join('sales_professionals s', 'c.freelancer_id = s.id', 'inner');
        $this->db->where('c.id', $chatId); 
        $query = $this->db->get();
        return $query->row_array();
    }

    public function insertMessage($data) {
        return $this->db->insert('messages', $data);
    }


    public function getUnreadMessageCount($user_id) {
        $this->db->select('id, chat_id, COUNT(*) as unread_count');
        $this->db->where('receiver_id', $user_id);
        $this->db->where('status', 'unread');
        $this->db->group_by('chat_id');
        $query = $this->db->get('messages');
        return $query->result_array();
    }

    public function markMessagesAsRead($chat_id, $user_id) {
        $this->db->where('chat_id', $chat_id);
        $this->db->where('receiver_id', $user_id);
        $this->db->where('status', 'unread');
        $this->db->update('messages', ['status' => 'read']);
    }
    
    
}

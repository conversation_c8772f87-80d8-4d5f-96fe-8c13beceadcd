<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class CourseModel extends CI_Model {
    public function __construct() {
        parent::__construct();
    }

    public function get_approved_courses($search = null)
{
    $today = date('Y-m-d');

    // Subquery for active promotions
    $subquery = "(SELECT reference_id, 1 as is_promoted 
                  FROM promotions 
                  WHERE type = 'course' 
                    AND start_date <= '{$today}' 
                    AND end_date >= '{$today}') as promo";

    $this->db->select('courses.*, COALESCE(promo.is_promoted, 0) as is_promoted');
    $this->db->from('courses');
    $this->db->join($subquery, 'promo.reference_id = courses.id', 'left');

    $this->db->where('courses.status', 'approved');

    // Apply search filter
    if (!empty($search)) {
        $this->db->group_start();
        $this->db->like('courses.title', $search);
        $this->db->or_like('courses.description', $search);
        $this->db->group_end();
    }

    // Promote courses with active promotion
    $this->db->order_by('is_promoted', 'DESC');
    $this->db->order_by('courses.id', 'DESC');

    return $this->db->get()->result();
}



public function get_approved_courses_for_home()
{
    $today = date('Y-m-d');

    // Subquery for active promotions
    $subquery = "(SELECT reference_id, 1 as is_promoted 
                  FROM promotions 
                  WHERE type = 'course' 
                    AND start_date <= '{$today}' 
                    AND end_date >= '{$today}') as promo";

    $this->db->select('courses.*, COALESCE(promo.is_promoted, 0) as is_promoted');
    $this->db->from('courses');
    $this->db->join($subquery, 'promo.reference_id = courses.id', 'left');

    $this->db->where('courses.status', 'approved');

    // Promote courses with active promotion
    $this->db->order_by('is_promoted', 'DESC');
    $this->db->order_by('courses.id', 'DESC');

    return $this->db->get()->result();
}

    
    

public function insert_course($data) {
    $this->db->insert('courses', $data);
}

public function update_course($id, $data) {
    $this->db->where('id', $id);
    $this->db->update('courses', $data);
}

public function get_course_by_id($id) {
    return $this->db->get_where('courses', ['id' => $id])->row();
}

public function get_user_courses($user_id, $type) {
    $this->db->select('*')
             ->from('courses')
             ->where('created_by', $user_id)
             ->where('created_type', $type);
    return $this->db->get()->result();
}

public function check_enrollment($user_id, $course_id, $type) {
    return $this->db->get_where('course_enrollments', [
        'user_id' => $user_id,
        'course_id' => $course_id,
        'user_type' => $type
    ])->row();
}

public function enroll_user($user_id, $course_id, $type) {
    $this->db->insert('course_enrollments', [
        'user_id' => $user_id,
        'course_id' => $course_id,
        'user_type'=> $type,
        'enrolled_at' => date('Y-m-d H:i:s')
    ]);
}

public function get_enrollment_history($user_id, $type)
{
    $this->db->select('course_enrollments.*, courses.title');
    $this->db->from('course_enrollments');
    $this->db->join('courses', 'courses.id = course_enrollments.course_id');
    if ($type === 'sales_professional') {
        $this->db->where('course_enrollments.user_id', $user_id);
        $this->db->where('course_enrollments.user_type', 'sales_professional');
    } else {
        $this->db->where('course_enrollments.user_id', $user_id);
        $this->db->where('course_enrollments.user_type', 'business');
    }
    return $this->db->get()->result();
}

public function get_course_enrollments($course_id)
{
    $this->db->select('course_enrollments.*, sp.full_name as sales_name');
    $this->db->from('course_enrollments');
    $this->db->join('sales_professionals sp', 'sp.id = course_enrollments.user_id', 'left');
    $this->db->where('course_enrollments.course_id', $course_id);
    return $this->db->get()->result();
}


}
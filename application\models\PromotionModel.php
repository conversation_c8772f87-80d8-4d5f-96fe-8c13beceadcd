<?php
class PromotionModel extends CI_Model {

    public function insert($data) {
        $this->db->insert('promotions', $data);
        return $this->db->insert_id();
    }

    public function mark_as_paid($promotion_id, $method = 'stripe') {
        return $this->db->where('id', $promotion_id)->update('promotions', [
            'payment_status' => 'paid',
            'payment_method' => $method
        ]);
    }

    public function get_active_promotions($type, $reference_id) {
        $today = date('Y-m-d');
        return $this->db->where([
            'promotion_type' => $type,
            'reference_id' => $reference_id,
            'payment_status' => 'paid'
        ])->where("start_date <=", $today)
          ->where("end_date >=", $today)
          ->get('promotions')->result();
    }

    public function get_all() {
        return $this->db->get('promotion_settings')->result();
    }
}

<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class SalesProfessionalModel extends CI_Model {
    public function __construct() {
        parent::__construct();
        ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);
    }

    public function get_sales_by_id($id) {
        // Get sales professional details
        $this->db->where('id', $id);
        $professional = $this->db->get('sales_professionals')->row();
    
        // Check if the sales professional exists
        if ($professional) {
            // Get average rating and total reviews
            $this->db->select('AVG(rating) as average_rating, COUNT(id) as review_count');
            $this->db->where('s_id', $id);
            $query = $this->db->get('reviews');
            $rating_data = $query->row();
            
            // Format the rating to one decimal place and set review count
            $professional->average_rating = number_format($rating_data->average_rating, 1);
            $professional->review_count = $rating_data->review_count;
        } else {
            // If no professional found, set default values
            $professional = new stdClass();
            $professional->average_rating = 0;
            $professional->review_count = 0;
        }
    
        return $professional;
    }
    

    public function update_sales($id, $data) {
        $this->db->where('id', $id);
        return $this->db->update('sales_professionals', $data);
    }

    public function get_bids_by_sales_professional($sales_professional_id) {
        $this->db->select('bids.*, assignments.title AS assignmentTitle, assignments.description AS assignmentDesc, assignments.budget, assignments.created_at as assignment_created_at');
        $this->db->from('bids');
        $this->db->join('assignments', 'bids.assignment_id = assignments.id');
        $this->db->where('bids.sales_professional_id', $sales_professional_id);
        $this->db->order_by('bids.created_at', 'DESC');
        $query = $this->db->get();
    
        return $query->result();
    }

    public function get_sales_professionals()
{
    $today = date('Y-m-d');

    // Subquery to determine active promotion
    $subquery = "(SELECT reference_id, 1 as is_promoted 
                  FROM promotions 
                  WHERE type = 'freelancer_profile' 
                    AND start_date <= '{$today}' 
                    AND end_date >= '{$today}') as promo";

    $this->db->select('sp.*, 
        (SELECT COUNT(r.id) FROM reviews r WHERE r.s_id = sp.id) AS review_count,
        (SELECT COALESCE(AVG(r.rating), 0) FROM reviews r WHERE r.s_id = sp.id) AS average_rating,
        COALESCE(promo.is_promoted, 0) as is_promoted'
    );

    $this->db->from('sales_professionals sp');
    $this->db->join($subquery, 'promo.reference_id = sp.id', 'left');

    // Now this will work, since is_promoted is a real alias
    $this->db->order_by('is_promoted', 'DESC');
    $this->db->order_by('sp.id', 'DESC');

    return $this->db->get()->result();
}

    public function search_sales_professionals($query, $industry, $location)
{
    $today = date('Y-m-d');

    // Subquery to determine active promotion
    $subquery = "(SELECT reference_id, 1 as is_promoted 
                  FROM promotions 
                  WHERE type = 'freelancer_profile' 
                    AND start_date <= '{$today}' 
                    AND end_date >= '{$today}') as promo";

    $this->db->select('sp.*, 
        (SELECT COUNT(r.id) FROM reviews r WHERE r.s_id = sp.id) AS review_count,
        (SELECT COALESCE(AVG(r.rating), 0) FROM reviews r WHERE r.s_id = sp.id) AS average_rating,
        COALESCE(promo.is_promoted, 0) as is_promoted'
    );

    $this->db->from('sales_professionals sp');
    $this->db->join($subquery, 'promo.reference_id = sp.id', 'left');

    // Filters
    if (!empty($query)) {
        $this->db->like('sp.full_name', $query);
    }
    if (!empty($industry)) {
        $this->db->where('sp.industry', $industry);
    }
    if (!empty($location)) {
        $this->db->where('sp.location', $location);
    }

    // Now this will work, since is_promoted is a real alias
    $this->db->order_by('is_promoted', 'DESC');
    $this->db->order_by('sp.id', 'DESC');

    return $this->db->get()->result();
}



    


    public function get_orders($status) {
        $sales_id = $this->session->userdata('s_id');
        $this->db->select('o.*, 
                          IF(o.is_custom_order = 1, of.title, a.title) AS title,
                           b.id as business_id,
                          b.business_name, 
                          b.profile_picture as business_image');
        $this->db->from('orders o');
        $this->db->join('businesses b', 'o.business_id = b.id', 'left');
        $this->db->join('offers of', 'o.offer_id = of.id', 'left');
        $this->db->join('assignments a', 'o.assignment_id = a.id', 'left');
        $this->db->where('o.freelancer_id', $sales_id);
        $this->db->where('o.status', $status);
        $query = $this->db->get();
        return $query->result();
    }



    public function get_reviews_by_sales_id($s_id) {
        $this->db->select('reviews.rating, reviews.review, reviews.created_at, businesses.business_name, businesses.profile_picture');
        $this->db->from('reviews');
        $this->db->join('businesses', 'reviews.user_id = businesses.id');
        $this->db->where('reviews.s_id', $s_id);
        $query = $this->db->get();
        return $query->result();
    }
    




    public function get_top_sales($filter)
    {
        // Determine the date range based on filter
        $dateCondition = '';
        if ($filter == 'yearly') {
            $dateCondition = "AND YEAR(o.deadline) = YEAR(CURDATE())";
        } elseif ($filter == 'monthly') {
            $dateCondition = "AND MONTH(o.deadline) = MONTH(CURDATE()) AND YEAR(o.deadline) = YEAR(CURDATE())";
        } elseif ($filter == 'weekly') {
            $dateCondition = "AND WEEK(o.deadline) = WEEK(CURDATE()) AND YEAR(o.deadline) = YEAR(CURDATE())";
        }
    
        // Get Top 10 Sales Professionals
        $query = $this->db->query("
            SELECT 
                sp.id, 
                sp.full_name, 
                sp.profile_picture, 
                sp.industry,
                COUNT(o.id) AS total_sales,
                (SELECT COALESCE(AVG(r.rating), 0) 
                 FROM reviews r 
                 WHERE r.s_id = sp.id) AS average_rating
            FROM sales_professionals sp
            INNER JOIN orders o ON sp.id = o.freelancer_id 
                AND o.status = 'completed' $dateCondition
            GROUP BY sp.id
            ORDER BY total_sales DESC, average_rating DESC
            LIMIT 10
        ");
    
        return $query->result();
    }
    

    
    public function set_default_bank_account($user_id, $bank_account_id) {
        // Reset all accounts to non-default
        $this->db->where('user_id', $user_id);
        $this->db->update('user_bank_accounts', ['is_default' => FALSE]);
    
        // Set the chosen account as default
        $this->db->where(['user_id' => $user_id, 'stripe_bank_account_id' => $bank_account_id]);
        $this->db->update('user_bank_accounts', ['is_default' => TRUE]);
    }
    
    public function get_default_bank_account($user_id) {
        return $this->db->get_where('user_bank_accounts', ['user_id' => $user_id, 'is_default' => TRUE])->row_array();
    }

    public function get_user_bank_accounts($user_id) {
        $this->db->where('user_id', $user_id);
        $this->db->order_by('is_default', 'DESC'); // Default bank first
        $query = $this->db->get('user_bank_accounts');
        return $query->result_array();
    }
    
    
}
<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class UserModel extends CI_Model {
    public function __construct() {
        parent::__construct();
    }


    public function signup($data, $tableName) {
        return $this->db->insert($tableName, $data); // Insert into the appropriate table
    }
    
    public function saveVerification($email, $token, $userType, $tableName) {
        // Depending on the user type, save verification token in either 'businesses' or 'sales_professionals'
        if ($userType == 'business') {
            $this->db->where('email', $email);
            return $this->db->update($tableName, ['verification_token' => $token]);
        } elseif ($userType == 'sales_professional') {
            $this->db->where('email', $email);
            return $this->db->update($tableName, ['verification_token' => $token]);
        }
    }


    public function checkEmailExists($email, $userType) {
        if ($userType == 'business') {
            $this->db->where('email', $email);
            $query = $this->db->get('businesses');
            return $query->row(); 
        } elseif ($userType == 'sales_professional') {
            $this->db->where('email', $email);
            $query = $this->db->get('sales_professionals');
            return $query->row(); 
        }
    }
    

    public function verifyAccountToken($token, $userType) {
        if ($userType == 'business') {
            $query = $this->db->get_where('businesses', ['verification_token' => $token]);
            return $query->row();
        } elseif ($userType == 'sales_professional') {
            $query = $this->db->get_where('sales_professionals', ['verification_token' => $token]);
            return $query->row();
        } else {
            return null;
        }
    }

    // Function to update the verification status of the account
    public function updateVerificationStatus($email, $userType) {
        $data = ['is_verified' => 1, 'verification_token' => null]; // Mark as verified and clear the token

        if ($userType == 'business') {
            $this->db->where('email', $email);
            return $this->db->update('businesses', $data);
        } elseif ($userType == 'sales_professional') {
            $this->db->where('email', $email);
            return $this->db->update('sales_professionals', $data);
        }

        return false;
    }

        function businessLogin($email){
            $this->db->where('email', $email);
            $query = $this->db->get('businesses');
            return $query->result();  
        }

        function salesLogin($email){
            $this->db->where('email', $email);
            $query = $this->db->get('sales_professionals');
            return $query->result();  
        }

        function find_password($email){
            $this->db->where('email', $email);
            $query = $this->db->get('users')->row_array();
            return $query;
        }

        function getByUserId($id){
            $this->db->where('id', $id);
            $query = $this->db->get('users');
            return $query->row();  
        }

        public function update($data) {
            $id = $this->session->userdata('userid');
            $this->db->where('id', $id);
            return $this->db->update('users', $data);
        }


        public function get_user_by_email($email, $type) {
        $table = ($type == 'sales') ? 'sales_professionals' : 'businesses';
        return $this->db->get_where($table, ['email' => $email])->row();
    }

    public function store_reset_token($email, $type, $token, $expiry) {
        $table = ($type == 'sales') ? 'sales_professionals' : 'businesses';
        $this->db->where('email', $email);
        $this->db->update($table, [
            'reset_token' => $token,
            'token_expiry' => $expiry
        ]);
    }

    public function get_user_by_token($type, $token) {
        $table = ($type == 'sales') ? 'sales_professionals' : 'businesses';
        $this->db->where('reset_token', $token);
        $this->db->where('token_expiry >=', date('Y-m-d H:i:s'));
        return $this->db->get($table)->row();
    }

    public function update_password($type, $token, $password) {
        $table = ($type == 'sales') ? 'sales_professionals' : 'businesses';
        $this->db->where('reset_token', $token);
        $this->db->update($table, [
            'password' => $password,
            'reset_token' => null,
            'token_expiry' => null
        ]);
    }
}

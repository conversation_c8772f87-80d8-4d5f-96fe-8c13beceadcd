<?php
require_once FCPATH . 'vendor/autoload.php';
use Stripe\Stripe;

class Withdraw_model extends CI_Model {

    public function __construct() {
        parent::__construct();
        Stripe::setApiKey('***********************************************************************************************************');
    }

    public function get_balance($user_id, $table) {
        $query = $this->db->get_where($table, ['id' => $user_id]);
        return $query->num_rows() > 0 ? (float)$query->row()->total_balance : 0.00;
    }

    public function get_user_bank_accounts($user_id, $type) {
        return $this->db->get_where('bank_accounts', ['id' => $user_id, 'user_type' => $type])->result();
    }

    public function get_or_create_stripe_account($user_id, $table) {
    $user = $this->db->get_where($table, ['id' => $user_id])->row();
    
    if ($user->stripe_account_id) {
        return $user->stripe_account_id;
    }


    try {
        $account = \Stripe\Account::create([
            'type' => 'express',
            'country' => 'US',
            'email' => $user->email,
            'capabilities' => [
                'transfers' => ['requested' => true],
            ],
        ]);
         
        // Save to DB
        $this->db->where('id', $user_id)->update($table, ['stripe_account_id' => $account->id]);

        return $account->id;

    } catch (\Stripe\Exception\ApiErrorException $e) {
        log_message('error', 'Stripe account creation failed: ' . $e->getMessage());
        return $e->getMessage();
    }
}


    public function sync_bank_accounts($user_id, $user_type, $stripe_account_id)
{

    try {
        $account = \Stripe\Account::retrieve($stripe_account_id);
        $banks = $account->external_accounts->all(['object' => 'bank_account']);

        foreach ($banks->data as $bank) {
            $exists = $this->db->get_where('bank_accounts', [
                'bank_account_id' => $bank->id,
                'user_id' => $user_id,
                'user_type' => $user_type
            ])->row();

            if (!$exists) {
                $this->db->insert('bank_accounts', [
                    'user_id' => $user_id,
                    'user_type' => $user_type,
                    'stripe_account_id' => $stripe_account_id,
                    'bank_account_id' => $bank->id,
                    'bank_name' => $bank->bank_name ?? 'Unknown',
                    'last4' => $bank->last4,
                    'created_at' => date('Y-m-d H:i:s')
                ]);
            }
        }
    } catch (Exception $e) {
        log_message('error', 'Bank Sync Error: ' . $e->getMessage());
    }
}


    public function make_payout($user_id, $table, $bank_id, $amount) {
        $balance = $this->get_balance($user_id, $table);
        if ($amount > $balance) {
            return ['success' => false, 'message' => 'Insufficient balance'];
        }

        $user = $this->db->get_where($table, ['id' => $user_id])->row();
        $bank = $this->db->get_where('bank_accounts', ['id' => $bank_id, 'user_id' => $user_id])->row();

        if (!$bank || !$user) {
            return ['success' => false, 'message' => 'Invalid account or user'];
        }

        try {
            $payout = \Stripe\Payout::create([
                'amount' => $amount * 100,
                'currency' => 'usd',
                'destination' => $bank->stripe_bank_id,
            ], [
                'stripe_account' => $user->stripe_account_id,
            ]);

            // Deduct balance
            $this->db->set('total_balance', 'total_balance - ' . (float)$amount, false);
            $this->db->where('id', $user_id);
            $this->db->update($table);

            return ['success' => true, 'payout_id' => $payout->id];

        } catch (Exception $e) {
            log_message('error', 'Stripe payout failed: ' . $e->getMessage());
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
}

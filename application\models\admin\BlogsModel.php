<?php
class BlogsModel extends CI_Model
{
    public function get()
    {
        $result = $this->db->select('*')->order_by('id', 'desc')->get("blogs")->result();
        return $result;
    }

    public function insert($data)
    {
        $this->db->insert("blogs", $data);
    }

    public function updateStatus($data, $id)
    {
        $this->db->where("id", $id);
        $this->db->update("blogs", $data);
    }

    public function updateIsDeletedStatus($data, $id)
    {
        $this->db->where("id", $id);
        $this->db->update("blogs", $data);
    }

    public function getBlogById($id)
    {
        $this->db->select("*");
        $this->db->where("id", $id);
        $query = $this->db->get("blogs");
        return $query->row_array();

    }

    public function updateBlog($data, $id)
    {
        $this->db->where("id", $id);
        $this->db->update("blogs", $data);
    }

    public function getauthorById($id)
    {
        $this->db->select("*");
        $this->db->where("id", $id);
        $query = $this->db->get("authors");
        return $query->row_array();

    }

}

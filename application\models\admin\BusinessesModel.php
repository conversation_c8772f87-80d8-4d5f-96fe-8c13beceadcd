<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class BusinessesModel extends CI_Model {


    public function get_all_businesses() {
        $this->db->select('b.*, 
            COUNT(o.id) AS total_orders, 
            SUM(CASE WHEN o.status = "completed" THEN 1 ELSE 0 END) AS completed_orders');
        $this->db->from('businesses b');
        $this->db->join('orders o', 'b.id = o.business_id', 'left');
        $this->db->group_by('b.id');
        
        $query = $this->db->get();
        return $query->result_array();
    }
    
    
}

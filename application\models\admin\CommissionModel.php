<?php
class CommissionModel extends CI_Model {

    public function get_all() {
        return $this->db->order_by('id', 'DESC')->get('commissions')->result();
    }

    public function get($id) {
        return $this->db->get_where('commissions', ['id' => $id])->row();
    }

    public function insert($data) {
        return $this->db->insert('commissions', $data);
    }

    public function update($id, $data) {
        return $this->db->update('commissions', $data, ['id' => $id]);
    }

    public function delete($id) {
        return $this->db->delete('commissions', ['id' => $id]);
    }
}

<?php
class DashboardModel extends CI_Model{
    public function totalOrders()
    {
        $query=$this->db->count_all('orders');
        return $query;
    }
    public function totalBusinesses()
    {   
        $query=$this->db->count_all('businesses');
        return $query;
    }
    public function totalSalesProfessionals()
    {
        $query=$this->db->count_all('sales_professionals');
        return $query;
    }
    public function todayOrders() {
        $this->db->select('*');
        $this->db->from('orders');
        $this->db->where('DATE(created_at)', date('Y-m-d'));  // Extract the date portion from created_at and compare with today's date
        return $this->db->count_all_results();
    }

    

    public function getMonthlyOrders() {
        $this->db->select('MONTH(created_at) as month, COUNT(*) as count');
        $this->db->from('orders');
        $this->db->group_by('MONTH(created_at)');
        $query = $this->db->get();
        return $query->result_array();
    }
    
    public function count_orders_by_month($month, $year) {
        $this->db->from('orders');
        $this->db->where('MONTH(created_at)', $month);
        $this->db->where('YEAR(created_at)', $year);
        return $this->db->count_all_results();
    }

    public function count_current_month_orders() {
        $current_month = date('m');
        $current_year = date('Y');
        return $this->count_orders_by_month($current_month, $current_year);
    }

    public function count_previous_month_orders() {
        $previous_month = date('m', strtotime('-1 month'));
        $year_of_previous_month = date('Y', strtotime('-1 month'));
        return $this->count_orders_by_month($previous_month, $year_of_previous_month);
    }
   
}
<?php
class LoginModel extends CI_Model{
    function insert_data($data){
         $this->db->insert("admin_user",$data);
         }

    

    public function change_password($data, $id)
    {
        $this->db->where("id", $id);
        $this->db->update("admin_user", $data);
    }

    public function get_user($username){
        $this->db->where('username', $username);
        $query = $this->db->get('admin_user'); // Assuming your table is named 'users'
    
        if($query->num_rows() == 1){
            return $query->row();
        } else {
            return false;
        }
    }
      

}
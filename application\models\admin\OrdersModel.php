<?php
class OrdersModel extends CI_Model
{
    public function get_all() {
        $this->db->select('
            o.*, 
            b.business_name,
            sp.full_name AS freelancer_name,
        ');
        $this->db->from('orders o');
        $this->db->join('businesses b', 'o.business_id = b.id', 'inner');
        $this->db->join('sales_professionals sp', 'o.freelancer_id = sp.id', 'inner');
        
        $query = $this->db->get();
        return $query->result();
    }
    


   
}

?>
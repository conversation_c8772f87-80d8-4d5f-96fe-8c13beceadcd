<?php
class PromotionSettingsModel extends CI_Model {

    public function get_all() {
        return $this->db->get('promotion_settings')->result();
    }

    public function get($id) {
        return $this->db->get_where('promotion_settings', ['id' => $id])->row();
    }

    public function insert($data) {
        return $this->db->insert('promotion_settings', $data);
    }

    public function update($id, $data) {
        return $this->db->where('id', $id)->update('promotion_settings', $data);
    }

    public function delete($id) {
        return $this->db->where('id', $id)->delete('promotion_settings');
    }
}

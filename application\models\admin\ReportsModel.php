<?php
class ReportsModel extends CI_Model{

    public function totalOrders($vendor = null, $branch_type = null)
{
    $this->db->select('orders.*, branches.branch_name'); // Select all columns from orders and branch_name from branches
    $this->db->from('orders');
    $this->db->join('branches', 'orders.branch_code = branches.branch_code'); // Join the branches table on branch_code
    
    // Add a condition for the vendor if provided
    if (!empty($vendor)) {
        $this->db->where('orders.vendor', $vendor);
    }
    
    // Add a condition for the branch type if provided
    if (!empty($branch_type)) {
        $this->db->where('branches.branch_type', $branch_type);
    }
    
    $query = $this->db->get(); // Execute the query
    return $query->result(); // Return the result set
}


    public function getVendors()
    {
        $result = $this->db->select('*')->order_by('id')->get("vendors")->result();
        return $result;
    }

    
    

}
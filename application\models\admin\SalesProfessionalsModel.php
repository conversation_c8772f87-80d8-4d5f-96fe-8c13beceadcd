<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class SalesProfessionalsModel extends CI_Model {


    public function get_all_sales_professionals() {
        $this->db->select('sp.*, 
            COUNT(o.id) AS total_orders, 
            SUM(CASE WHEN o.status = "completed" THEN 1 ELSE 0 END) AS completed_orders');
        $this->db->from('sales_professionals sp');
        $this->db->join('orders o', 'sp.id = o.freelancer_id', 'left');
        $this->db->group_by('sp.id');
        
        $query = $this->db->get();
        return $query->result_array();
    }
    
    
}

<?php
class UsersModel extends CI_Model
{
    public function get()
    {
        $result = $this->db->select('*')->order_by('id')->get("users")->result();
        return $result;
    }

    public function get_by_id($id)
{
    $this->db->select('subscriptions.*, packages.*, subscriptions.price AS paidAmount');
    $this->db->from('subscriptions');
    $this->db->join('packages', 'subscriptions.plan = packages.id', 'left');
    $this->db->where('subscriptions.user_id', $id);
    $query = $this->db->get();
    return $query->result();
}


   
}

?>
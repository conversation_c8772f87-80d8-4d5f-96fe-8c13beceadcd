<main class="main">

    <!-- Page Title -->
    <div class="page-title" data-aos="fade">
        <div class="heading">
            <div class="container">
                <div class="row d-flex justify-content-center text-center">
                    <div class="col-lg-8">
                        <h1>Forgot Password</h1>
                    </div>
                </div>
            </div>
        </div>
        <nav class="breadcrumbs">
            <div class="container">
                <ol>
                    <li><a href="<?=base_url()?>">Home</a></li>
                    <li class="current">Get Your Password</li>
                </ol>
            </div>
        </nav>
    </div><!-- End Page Title -->

    <div class="section">
        <div class="container mt-5">
            <?php if ($this->session->flashdata('error')): ?>
            <div class="alert alert-danger"><?= $this->session->flashdata('error') ?></div>
            <?php endif; ?>
            <?php if ($this->session->flashdata('success')): ?>
            <div class="alert alert-success"><?= $this->session->flashdata('success') ?></div>
            <?php endif; ?>

            <ul class="nav nav-pills register-tabs" id="registrationTabs" role="tablist">

            <li class="nav-item" role="presentation">
                    <button class="nav-link active custom-tab" id="sales-professional-tab" data-bs-toggle="pill"
                        data-bs-target="#sales" type="button" role="tab" aria-controls="sales-professional"
                        aria-selected="true">Sales
                        Professional</button>

                <li class="nav-item" role="presentation">
                    <button class="nav-link custom-tab" id="business-tab" data-bs-toggle="pill"
                        data-bs-target="#business" type="button" role="tab" aria-controls="business"
                        aria-selected="false">Business</button>
                </li>
                

            </ul>

            <div class="tab-content mt-3" id="registrationTabsContent">
                <div class="tab-pane fade show active p-3" id="sales" role="tabpanel" aria-labelledby="sales-professional-tab">
                    <form method="post" action="<?= base_url('forgot/send_reset_link') ?>">
                        <input type="hidden" name="user_type" value="sales">
                        <div class="form-group">
                            <label>Email</label>
                            <input type="email" placeholder='Enter Sales Professional Email Address' name="email"
                                class="form-control" required>
                        </div>
                        <button class="btn btn-success mt-3">Send Reset Link</button>
                    </form>
                </div>

                <div class="tab-pane fade p-3" id="business" role="tabpanel" aria-labelledby="business-tab">
                    <form method="post" action="<?= base_url('forgot/send_reset_link') ?>">
                        <input type="hidden" name="user_type" value="business">
                        <div class="form-group">
                            <label>Email</label>
                            <input type="email" placeholder='Enter Bussiness Email Address' name="email"
                                class="form-control" required>
                        </div>
                        <button class="btn btn-success mt-3">Send Reset Link</button>
                    </form>
                </div>
            </div>
        </div>

    </div>
</main>
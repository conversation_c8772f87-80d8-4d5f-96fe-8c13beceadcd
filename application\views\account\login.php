<main class="main">
    <section class='section'>
        <div class="container">
            <div class="card">
                <div class="row">
                    <div class="col-md-6">
                        <img src="<?=base_url()?>assets/img/account.png" class="img-fluid h-100"
                            alt="Registration Image" style="object-fit: cover;">
                    </div>
                    <div class="col-md-6">
                        <div class="card-body">
                            <h2 class="card-title mt-3 mb-5">Welcome Back!</h2>
                            <form method="POST" action="<?php echo base_url('auth/do_login'); ?>">
                                <?php
                                    if($this->session->flashdata('login_error'))
                                    {
                                        echo '
                                        <div class="alert alert-danger">
                                        '.$this->session->flashdata("login_error").'
                                        </div>
                                        ';
                                    }
                                ?>
                                <div class="mb-3">
                                    <label for="email" class="form-label">Email</label>
                                    <input type="email"
                                        value="<?php if (get_cookie('sales_email')) { echo get_cookie('sales_email'); } ?>"
                                        class="form-control" id="email" name="email" required>
                                </div>
                                <div class="mb-3">
                                    <label for="password" class="form-label">Password</label>
                                    <input type="password"
                                        value="<?php if (get_cookie('sales_password')) { echo get_cookie('sales_password'); } ?>"
                                        class="form-control" id="password" name="password" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">User Type</label>
                                    <div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" id="business" name="user_type"
                                                value="business" <?php if (get_cookie('business_remember')) { echo "checked"; } ?> checked>
                                            <label class="form-check-label" for="business">Business</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" id="sales_professional"
                                                name="user_type" value="sales_professional" <?php if (get_cookie('business_remember')) { echo "checked"; } ?>>
                                            <label class="form-check-label" for="sales_professional">Sales
                                                Professional</label>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-check-label ml-0">
                                        <input class="mb-0 ml-0 mt-0" name="remember"
                                            <?php if (get_cookie('sales_email')) { ?> checked="checked" <?php } ?>
                                            type="checkbox">&nbsp; &nbsp; Remember me
                                    </label>
                                </div>
                                <div class="mb-3">
                                    <input type="submit" value="Login" class="w-100 d-block btn btn-success" />
                                </div>
                                <div class="d-flex justify-content-between align-items-center">
                                    <a href="<?=base_url()?>register">Don't have an account?</a>
                                    <a class='text-danger' href="<?=base_url()?>forgot-password">Forgot Password?</a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</main>
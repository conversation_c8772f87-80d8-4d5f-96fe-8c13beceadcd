<main class="main">
    <section class='section'>
        <div class="container">
            <div class="card">
                <div class="row">
                    <div class="col-md-6">
                        <img src="<?=base_url()?>assets/img/account.png" class="img-fluid h-100"
                            alt="Registration Image" style="object-fit: cover;">
                    </div>
                    <div class="col-md-6">
                        <div class="card-body">
                            <h2 class="card-title mt-3 mb-5">Create Account</h2>
                            <ul class="nav nav-pills register-tabs" id="registrationTabs" role="tablist">
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link active custom-tab" id="business-tab" data-bs-toggle="pill"
                                        data-bs-target="#business" type="button" role="tab" aria-controls="business"
                                        aria-selected="true">Business</button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link custom-tab" id="sales-professional-tab"
                                        data-bs-toggle="pill" data-bs-target="#sales-professional" type="button"
                                        role="tab" aria-controls="sales-professional" aria-selected="false">Sales
                                        Professional</button>
                                </li>
                            </ul>
                            <div class="tab-content mt-3" id="registrationTabsContent">
                                <!-- Business Registration Form -->
                                <div class="tab-pane fade show active" id="business" role="tabpanel"
                                    aria-labelledby="business-tab">
                                    <form id="businessForm" method="POST">
                                        <div class="mb-3">
                                            <label for="referelCode" class="form-label">Referral Code (optional)</label>
                                            <input type="text" class="form-control" id="referelCode"
                                                name="referral_code">
                                        </div>
                                        <div class="mb-3">
                                            <label for="businessName" class="form-label">Business Name</label>
                                            <input type="text" class="form-control" id="businessName"
                                                placeholder='Business Name' name="business_name" required>
                                        </div>
                                        <div class="mb-3">
                                            <label for="businessIndustry" class="form-label">Industry</label>
                                            <select class="form-control" id="businessIndustry" name="industry" required>
                                                <option value="" disabled selected>Select Industry</option>
                                                <option value="Information Technology">Information Technology</option>
                                                <option value="Healthcare">Healthcare</option>
                                                <option value="Finance">Finance</option>
                                                <option value="Education">Education</option>
                                                <option value="Manufacturing">Manufacturing</option>
                                                <option value="Retail">Retail</option>
                                                <option value="Construction">Construction</option>
                                                <option value="Transportation">Transportation</option>
                                                <option value="Entertainment">Entertainment</option>
                                                <option value="Other">Other</option>
                                            </select>
                                        </div>

                                        <div class="mb-3">
                                            <label for="businessEmail" class="form-label">Email</label>
                                            <input type="email" class="form-control" id="businessEmail" name="email"
                                                placeholder='Email' required>
                                        </div>
                                        <div class="mb-3">
                                            <label for="kvk-vat" class="form-label">KVK/VAT Number</label>
                                            <input type="text" class="form-control" id="kvk-vat"
                                                name="kvk_vat" required>
                                        </div>
                                        <div class="mb-3">
                                            <label for="businessPassword" class="form-label">Password</label>
                                            <input type="password" class="form-control" id="businessPassword"
                                                placeholder='Password' name="password" required>
                                        </div>
                                        <div class="mb-3">
                                            <input type="submit" value="Register"
                                                class="w-100 d-block btn btn-success" />
                                        </div>
                                        <div class="text-center">
                                            <a href="<?=base_url()?>login">Already have an account?</a>
                                        </div>
                                    </form>
                                </div>

                                <!-- Sales Professional Registration Form -->
                                <div class="tab-pane fade" id="sales-professional" role="tabpanel"
                                    aria-labelledby="sales-professional-tab">
                                    <form id="salesProfessionalForm" method="POST">
                                        <div class="mb-3">
                                            <label for="referelCode" class="form-label">Referral Code (optional)</label>
                                            <input type="text" class="form-control" id="referelCode"
                                                name="referral_code">
                                        </div>

                                        <div class="mb-3">
                                            <label for="professionalName" class="form-label">Full Name</label>
                                            <input type="text" class="form-control" id="professionalName"
                                                name="full_name" required>
                                        </div>
                                        <div class="mb-3">
                                            <label for="businessIndustry" class="form-label">Industry</label>
                                            <select class="form-control" id="businessIndustry" name="industry" required>
                                                <option value="" disabled selected>Select Industry</option>
                                                <option value="Information Technology">Information Technology</option>
                                                <option value="Healthcare">Healthcare</option>
                                                <option value="Finance">Finance</option>
                                                <option value="Education">Education</option>
                                                <option value="Manufacturing">Manufacturing</option>
                                                <option value="Retail">Retail</option>
                                                <option value="Construction">Construction</option>
                                                <option value="Transportation">Transportation</option>
                                                <option value="Entertainment">Entertainment</option>
                                                <option value="Other">Other</option>
                                            </select>
                                        </div>
                                        <div class="mb-3">
                                            <label for="location" class="form-label">Location</label>
                                            <input type="text" class="form-control" id="location" name="location"
                                                required>
                                        </div>
                                        <div class="mb-3">
                                            <label for="kvk-vat" class="form-label">KVK/VAT Number</label>
                                            <input type="text" class="form-control" id="kvk-vat"
                                                name="kvk_vat" required>
                                        </div>
                                        <div class="mb-3">
                                            <label for="professionalEmail" class="form-label">Email</label>
                                            <input type="email" class="form-control" id="professionalEmail" name="email"
                                                required>
                                        </div>
                                        <div class="mb-3">
                                            <label for="professionalPassword" class="form-label">Password</label>
                                            <input type="password" class="form-control" id="professionalPassword"
                                                name="password" required>
                                        </div>
                                        <div class="mb-3">
                                            <input type="submit" value="Register"
                                                class="w-100 d-block btn btn-success" />
                                        </div>
                                        <div class="text-center">
                                            <a href="<?=base_url()?>login">Already have an account?</a>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</main>
<script>
$(document).ready(function() {
    // Handle Business Form Submission
    $('#businessForm').submit(function(event) {
        event.preventDefault();
        submitForm($(this), 'business');
    });

    // Handle Sales Professional Form Submission
    $('#salesProfessionalForm').submit(function(event) {
        event.preventDefault();
        submitForm($(this), 'sales_professional');
    });

    function submitForm(form, userType) {
        // Show SweetAlert loading spinner
        Swal.fire({
            title: 'Processing...',
            text: 'Please wait while we process your request.',
            allowOutsideClick: false,
            didOpen: () => {
                Swal.showLoading();
            }
        });

        $.ajax({
            url: '<?= base_url("Auth/signup") ?>',
            type: 'POST',
            data: form.serialize() + '&user_type=' + userType,
            success: function(response) {
                Swal.close(); // Close the loading spinner
                let res = JSON.parse(response);
                if (res.status === 'success') {
                    Swal.fire('Success!', res.message, 'success').then(() => {
                        // Optional: Redirect or reload the page after success
                    });
                } else {
                    Swal.fire('Error!', res.message, 'error');
                }
            },
            error: function() {
                Swal.close(); // Close the loading spinner
                Swal.fire('Error!', 'There was an error processing your request. Please try again.',
                    'error');
            }
        });
    }
});
</script>


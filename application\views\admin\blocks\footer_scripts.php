</div>
<!-- ============================================================== -->
<!-- End Right content here -->
<!-- ============================================================== -->

</div>
<!-- END wrapper -->

<!-- jQuery  -->

<script src="<?=base_url()?>assets/admin/js/bootstrap.bundle.min.js"></script>
<script src="<?=base_url()?>assets/admin/js/metismenu.min.js"></script>
<script src="<?=base_url()?>assets/admin/js/jquery.slimscroll.js"></script>
<script src="<?=base_url()?>assets/admin/js/waves.min.js"></script>

<script src="<?=base_url()?>assets/admin/plugins/apexchart/apexcharts.min.js"></script>
<script src="<?=base_url()?>assets/admin/plugins/bootstrap-datepicker/js/bootstrap-datepicker.min.js"></script>

<!-- Required datatable js -->
<script src="<?=base_url()?>assets/admin/plugins/datatables/jquery.dataTables.min.js"></script>
<script src="<?=base_url()?>assets/admin/plugins/datatables/dataTables.bootstrap4.min.js"></script>
<!-- Buttons examples -->
<script src="<?=base_url()?>assets/admin/plugins/datatables/dataTables.buttons.min.js"></script>
<script src="<?=base_url()?>assets/admin/plugins/datatables/buttons.bootstrap4.min.js"></script>
<script src="<?=base_url()?>assets/admin/plugins/datatables/jszip.min.js"></script>
<script src="<?=base_url()?>assets/admin/plugins/datatables/pdfmake.min.js"></script>
<script src="<?=base_url()?>assets/admin/plugins/datatables/vfs_fonts.js"></script>
<script src="<?=base_url()?>assets/admin/plugins/datatables/buttons.html5.min.js"></script>
<script src="<?=base_url()?>assets/admin/plugins/datatables/buttons.print.min.js"></script>
<script src="<?=base_url()?>assets/admin/plugins/datatables/buttons.colVis.min.js"></script>
<!-- Responsive examples -->
<script src="<?=base_url()?>assets/admin/plugins/datatables/dataTables.responsive.min.js"></script>
<script src="<?=base_url()?>assets/admin/plugins/datatables/responsive.bootstrap4.min.js"></script>

<!-- Datatable init js -->
<script src="<?=base_url()?>assets/admin/pages/datatables.init.js"></script>

<!--Morris Chart-->
<script src="<?=base_url()?>assets/admin/plugins/morris/morris.min.js"></script>
<script src="<?=base_url()?>assets/admin/plugins/raphael/raphael.min.js"></script>

<!-- <script src="<?=base_url()?>assets/pages/dashboard.init.js"></script> -->
<script>
! function($) {
    "use strict";

    var Dashboard = function() {};

    //creates Stacked chart
    Dashboard.prototype.createStackedChart = function(element, data, xkey, ykeys, labels, lineColors) {
            Morris.Bar({
                element: element,
                data: data,
                xkey: xkey,
                ykeys: ykeys,
                stacked: true,
                labels: labels,
                hideHover: 'auto',
                barSizeRatio: 0.4,
                resize: true, //defaulted to true
                gridLineColor: '#eeeeee',
                barColors: lineColors
            });
        },


        //creates Donut chart
        Dashboard.prototype.createDonutChart = function(element, data, colors) {
            Morris.Donut({
                element: element,
                data: data,
                resize: true, //defaulted to true
                colors: colors
            });
        },

        Dashboard.prototype.init = function() {

            //creating Stacked chart
            var $stckedData = <?php echo json_encode($graph_data); ?>;
            this.createStackedChart('morris-bar-stacked', $stckedData, 'y', ['a'], ['Orders'], ['#23cbe0']);

            var $donutData = [

                {
                    label: "Current Month Orders",
                    value: <?=$currentmonthorders?>
                },
                {
                    label: "Previous Month Orders",
                    value: <?=$previousmonthorders?>
                }
            ];
            this.createDonutChart('morris-donut-example', $donutData, ['#0e86e7', '#23cbe0']);
        },
        //init
        $.Dashboard = new Dashboard, $.Dashboard.Constructor = Dashboard
}(window.jQuery),

//initializing
function($) {
    "use strict";
    $.Dashboard.init();
}(window.jQuery);


//   apex chart
var options1 = {
    chart: {
        type: 'area',
        height: 60,
        sparkline: {
            enabled: true
        }
    },
    series: [{
        data: [24, 66, 42, 88, 62, 24, 45, 12, 36, 10]
    }],
    stroke: {
        curve: 'smooth',
        width: 3
    },
    colors: ['#0e86e7'],
    tooltip: {
        fixed: {
            enabled: false
        },
        x: {
            show: false
        },
        y: {
            title: {
                formatter: function(seriesName) {
                    return ''
                }
            }
        },
        marker: {
            show: false
        }
    }
}
new ApexCharts(document.querySelector("#chart1"), options1).render();
// 2
var options2 = {
    chart: {
        type: 'area',
        height: 60,
        sparkline: {
            enabled: true
        }
    },
    series: [{
        data: [54, 12, 24, 66, 42, 25, 44, 12, 36, 9]
    }],
    stroke: {
        curve: 'smooth',
        width: 3
    },
    colors: ['#fbb131'],
    tooltip: {
        fixed: {
            enabled: false
        },
        x: {
            show: false
        },
        y: {
            title: {
                formatter: function(seriesName) {
                    return ''
                }
            }
        },
        marker: {
            show: false
        }
    }
}
new ApexCharts(document.querySelector("#chart2"), options2).render();
// 3
var options3 = {
    chart: {
        type: 'area',
        height: 60,
        sparkline: {
            enabled: true
        }
    },
    series: [{
        data: [10, 36, 12, 44, 63, 24, 44, 12, 56, 24]
    }],
    stroke: {
        curve: 'smooth',
        width: 3
    },
    colors: ['#23cbe0'],
    tooltip: {
        fixed: {
            enabled: false
        },
        x: {
            show: false
        },
        y: {
            title: {
                formatter: function(seriesName) {
                    return ''
                }
            }
        },
        marker: {
            show: false
        }
    }
}
new ApexCharts(document.querySelector("#chart3"), options3).render();
//   4
var options4 = {
    chart: {
        type: 'area',
        height: 60,
        sparkline: {
            enabled: true
        }
    },
    series: [{
        data: [34, 66, 42, 33, 62, 24, 45, 16, 48, 10]
    }],
    stroke: {
        curve: 'smooth',
        width: 3
    },
    colors: ['#fb4365'],
    tooltip: {
        fixed: {
            enabled: false
        },
        x: {
            show: false
        },
        y: {
            title: {
                formatter: function(seriesName) {
                    return ''
                }
            }
        },
        marker: {
            show: false
        }
    }
}
new ApexCharts(document.querySelector("#chart4"), options4).render();
</script>

<!-- App js -->
<script src="<?=base_url()?>assets/admin/js/app.js"></script>

</body>

</html>
<div class="content">

    <div class="container-fluid">
        <div class="page-title-box">

            <div class="row align-items-center ">
                <div class="col-md-12">
                    <div class="page-title-box">
                        <h4 class="page-title">Blog Add</h4>

                    </div>
                </div>
            </div>
        </div>
        <!-- end page-title -->

        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <form action="<?=site_url()?>master/blogs/save" method="POST" enctype="multipart/form-data">
                            <?php

if($this->session->flashdata('msg-type') == "success")
{
echo '
<div class="alert alert-success mb-2">
'.$this->session->flashdata("msg").'
</div>
';
}
?>

                            <?php

if($this->session->flashdata('msg-type') == "error")
{
echo '
<div class="alert alert-danger mb-2">
'.$this->session->flashdata("msg").'
</div>
';
}
?>


                            <div class="form-group row">
                                <label class="col-sm-2 col-form-label">Title</label>
                                <div class="col-sm-10">
                                    <input type="text" class="form-control" name="title" id="title" required>
                                </div>
                            </div>


                            <div class="form-group row">
                                <label class="col-sm-2 col-form-label">Route</label>
                                <div class="col-sm-10">
                                    <input type="text" class="form-control form-control-sm" name="route" id="route"
                                        required>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label class="col-sm-2 col-form-label">Blog Image</label>
                                <div class="col-sm-10">
                                    <input type="file" name="banner" class="form-control" id="banner" required>
                                </div>
                            </div>


                            <div class="form-group row">
                                <label class="col-sm-2 col-form-label">Description</label>
                                <div class="col-sm-10">
                                    <textarea name="desc" class="form-control form-control-sm" cols="5" id="desc"
                                        rows="3" required></textarea>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label class="col-sm-2 col-form-label">Body</label>
                                <div class="col-sm-10">
                                    <textarea id="editor1" name="body" cols="5" rows="10"
                                        class="form-control"></textarea>
                                </div>
                            </div>




                            <div class="form-group mb-0">
                                <div class='text-center'>
                                    <button type="submit" class="btn btn-primary waves-effect waves-light">
                                        Save
                                    </button>
                                    <button type="reset" class="btn btn-secondary waves-effect m-l-5">
                                        Reset
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <!-- end col -->
        </div>
        <!-- end row -->

    </div>
    <!-- container-fluid -->

</div>
<!-- content -->
<script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>

<script>
CKEDITOR.replace('editor1');
</script>
<div class="content">

    <div class="container-fluid">
        <div class="page-title-box">

            <div class="row align-items-center ">
                <div class="col-md-12">
                    <div class="page-title-box">
                        <h4 class="page-title">Blog Edit</h4>

                    </div>
                </div>
            </div>
        </div>
        <!-- end page-title -->

        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <form action="<?=site_url()?>master/blogs/update" method="POST" enctype="multipart/form-data">
            
                            <div class="form-group row">
                                <label class="col-sm-2 col-form-label">Title</label>
                                <div class="col-sm-10">
                                    <input type="text" class="form-control" value='<?=$blog['title']?>' name="title" id="title" required>
                                </div>
                            </div>


                            <div class="form-group row">
                                <label class="col-sm-2 col-form-label">Route</label>
                                <div class="col-sm-10">
                                    <input type="text" class="form-control form-control-sm" value='<?=$blog['route']?>' name="route" id="route"
                                        required>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label class="col-sm-2 col-form-label">Blog Image</label>
                                <div class="col-sm-10">
                                    <input type="file" name="banner" class="form-control" id="banner">
                                    <img style="width:18%; margin-left:15px; height:220px; text:center" src="<?= base_url(); ?>assets/img/blog/<?= $blog['banner']; ?>" alt="Image doesn't exist">
                                </div>
                            </div>

                            <input type="hidden" name='id' value='<?= $this->input->get("id")?>'>

                            <div class="form-group row">
                                <label class="col-sm-2 col-form-label">Description</label>
                                <div class="col-sm-10">
                                    <textarea name="desc" class="form-control form-control-sm" cols="5" id="desc"
                                        rows="3" required><?=$blog['desc']?></textarea>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label class="col-sm-2 col-form-label">Body</label>
                                <div class="col-sm-10">
                                    <textarea id="editor1" name="body" cols="5" rows="10"
                                        class="form-control"><?=$blog['body']?></textarea>
                                </div>
                            </div>




                            <div class="form-group mb-0">
                                <div class='text-center'>
                                    <button type="submit" class="btn btn-primary waves-effect waves-light">
                                        Save
                                    </button>
                                    <button type="reset" class="btn btn-secondary waves-effect m-l-5">
                                        Reset
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <!-- end col -->
        </div>
        <!-- end row -->

    </div>
    <!-- container-fluid -->

</div>
<!-- content -->
<script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>

<script>
CKEDITOR.replace('editor1');
</script>
 <!-- Start content -->
 <div class="content">

     <div class="container-fluid">
         <div class="page-title-box">

             <div class="row align-items-center ">
                 <div class="col-md-8">
                     <div class="page-title-box">
                         <h4 class="page-title">Blog's Record</h4>

                     </div>
                 </div>

                 <div class="col-md-4">
                     <div class="page-title-box text-right">
                         <a class='btn btn-primary' href="<?=site_url()?>master/blogs/add">Add Blog</a>

                     </div>
                 </div>



             </div>
         </div>
         <!-- end page-title -->

         <div class="row">
             <div class="col-12">
                 <div class="card">
                     <div class="card-body">
                         <div class="table-responsive">

                             <?php if(count($list) > 0) {?>


                             <table id="datatable-buttons"
                                 class="table table-bordered table-striped dt-responsive nowrap"
                                 style="border-collapse: collapse; border-spacing: 0; width: 100%;">
                                 <thead>
                                     <tr>
                                         <th>Id</th>
                                         <th>Banner</th>
                                         <th>Title</th>
                                         <th>Status</th>
                                         <th>Action</th>
                                     </tr>

                                 </thead>

                                 <tbody>
                                     <?php 
                    foreach ($list as $key => $item) { ?>
                                     <tr data-id="<?= $item->id ?>" class="blog-status-<?= $item->status ?>"
                                         data-status="<?= $item->status ?>">
                                         <td><?= $item->id ?></td>
                                         <td><img style="width:150px; height:100px;"
                                                 src="<?= base_url() ?>assets/frontend/img/blog/<?= $item->banner ?>"
                                                 alt=""> </td>
                                         <td><?= $item->title ?></td>
                                         <td><?= $item->status == 1 ? 'Active' : 'Inactive' ?></td>
                                         <td>
                                             <div class="btn-group">
                                                 <button class="btn btn-sm btn-primary dropdown-toggle" type="button"
                                                     data-toggle="dropdown" aria-haspopup="true"
                                                     aria-expanded="false">Action</button>
                                                 <div class="dropdown-menu">
                                                     <a class="dropdown-item"
                                                         href="<?= site_url() ?>master/blogs/edit?id=<?= $item->id ?>&status=0">Edit</a>
                                                     <?php if ($item->status == 1) { ?>
                                                     <a class="dropdown-item action-btn"
                                                         href="<?= site_url() ?>master/blogs/update-status?id=<?= $item->id ?>&status=0"
                                                         data-action="inactivate">Inactivate</a>
                                                     <?php } else { ?>
                                                     <a class="dropdown-item action-btn"
                                                         href="<?= site_url() ?>master/blogs/update-status?id=<?= $item->id ?>&status=1"
                                                         data-action="activate">Activate</a>
                                                     <?php } ?>

                                                     <?php if ($item->is_deleted == 1) { ?>
                                                     <a class="dropdown-item"
                                                         href="<?= site_url() ?>master/blogs/update-delete?id=<?= $item->id ?>&status=0">Recover</a>
                                                     <?php } else { ?>
                                                     <a class="dropdown-item"
                                                         href="<?= site_url() ?>master/blogs/update-delete?id=<?= $item->id ?>&status=1">Delete</a>
                                                     <?php } ?>
                                                     <a class="dropdown-item" target="_blank"
                                                         href="<?= site_url() ?><?= $item->route ?>">Preview</a>
                                                 </div>
                                             </div>
                                         </td>
                                     </tr>
                                     <?php } ?>
                                 </tbody>
                             </table>
                         </div>
                         <?php
                    }else{
                    ?>
                         <div class="alert alert-danger wow fadeInUp" role="alert"> No Data Found! </div>
                         <?php
                    }
                    ?>
                     </div>
                 </div>
             </div>
             <!-- end col -->
         </div>
         <!-- end row -->



     </div>
     <!-- container-fluid -->

 </div>
 <!-- content -->
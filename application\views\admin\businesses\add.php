<!-- Start content -->
<div class="content">

    <div class="container-fluid">
        <div class="page-title-box">

            <div class="row align-items-center ">
                <div class="col-md-12">
                    <div class="page-title-box">
                        <h4 class="page-title">Package Add</h4>

                    </div>
                </div>
            </div>
        </div>
        <!-- end page-title -->

        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <form method='POST' enctype="multipart/form-data" action="<?=site_url();?>master/packages/save">
                            <?php if($this->session->flashdata('package-add')) { ?>
                            <div class="alert alert-success mb-2"><?= $this->session->flashdata("package-add"); ?></div>
                            <?php } ?>

                            <?php if($this->session->flashdata('package-error')) { ?>
                            <div class="alert alert-danger mb-2"><?= $this->session->flashdata("package-error"); ?>
                            </div>
                            <?php } ?>

                            <div class="form-group row">
                                <label class="col-sm-2 col-form-label" for="package-title">Package Title</label>
                                <div class="col-sm-10">
                                    <input type="text" class="form-control" id="package-title" name="package_title"
                                        required>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label class="col-sm-2 col-form-label" for="package-type">Package Type</label>
                                <div class="col-sm-10">
                                    <select class="form-control" id="package-type" name="package_type" required>
                                        <option value="free">Free</option>
                                        <option value="paid">Paid</option>
                                    </select>
                                </div>
                            </div>



                            

                            <!-- Paid Package Options -->
                            <div id="paid-package-options" class="d-none">
                                <div class="form-group row">
                                    <label class="col-sm-2 col-form-label" for="paid-package-type">Paid Package
                                        Type</label>
                                    <div class="col-sm-10">
                                        <select class="form-control" id="paid-package-type" name="paid_package_type">
                                            <option value="single">Single</option>
                                            <option value="variable">Variable</option>
                                        </select>
                                    </div>
                                </div>

                                <!-- Single Option -->
                                <div id="single-options">
                                    <div class="form-group row">
                                        <label class="col-sm-2 col-form-label" for="single-duration">Duration</label>
                                        <div class="col-sm-10">
                                            <select class="form-control" id="single-duration" name="single_duration">
                                                <option value="monthly">Monthly</option>
                                                <option value="yearly">Yearly</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-sm-2 col-form-label" for="single-price">Price</label>
                                        <div class="col-sm-10">
                                            <input type="number" step="0.01" class="form-control" id="single-price"
                                                name="single_price" placeholder="Price">
                                        </div>
                                    </div>
                                </div>

                                <!-- Variable Option -->
                                <div id="variable-options" class="d-none">
                                    <div id="variant-container">
                                        <div class="form-group row">
                                            <label class="col-sm-2 col-form-label">Variants</label>
                                            <div class="col-sm-10">
                                                <div class="input-group mb-2">
                                                    <select class="form-control" name="variable_duration[]">
                                                        <option value="monthly">Monthly</option>
                                                        <option value="yearly">Yearly</option>
                                                    </select>
                                                    <input type="number" step="0.01" class="form-control"
                                                        name="variable_price[]" placeholder="Price">
                                                    <div class="input-group-append">
                                                        <button type="button"
                                                            class="btn btn-success add-variant">+</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>


                            <!-- Characteristics Section -->
                            <div class="form-group row">
                                <label class="col-sm-2 col-form-label">Characteristics</label>
                                <div class="col-sm-10">
                                    <div id="characteristics-container">
                                        <div class="input-group mb-2">
                                            <input type="text" class="form-control" name="characteristics[]"
                                                placeholder="Characteristic">
                                            <div class="input-group-append">
                                                <button type="button"
                                                    class="btn btn-success add-characteristic">+</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group mb-0 text-center">
                                <button type="submit" class="btn btn-primary waves-effect waves-light">Save</button>
                                <button type="reset" class="btn btn-secondary waves-effect m-l-5">Reset</button>
                            </div>
                        </form>

                    </div>
                </div>
            </div>
            <!-- end col -->
        </div>
        <!-- end row -->

    </div>
    <!-- container-fluid -->

</div>
<!-- content -->

<script>
$(document).ready(function() {
    // Toggle package options based on package type
    $('#package-type').change(function() {
        if ($(this).val() === 'free') {
            $('#free-package-options').removeClass('d-none');
            $('#paid-package-options').addClass('d-none');
        } else {
            $('#paid-package-options').removeClass('d-none');
            $('#free-package-options').addClass('d-none');
        }
    });

    // Toggle options for paid package types
    $('#paid-package-type').change(function() {
        if ($(this).val() === 'single') {
            $('#single-options').removeClass('d-none');
            $('#variable-options').addClass('d-none');
        } else {
            $('#variable-options').removeClass('d-none');
            $('#single-options').addClass('d-none');
        }
    });

    // Add new characteristic field
    $(document).on('click', '.add-characteristic', function() {
        var newField = `<div class="input-group mb-2">
                            <input type="text" class="form-control" name="characteristics[]" placeholder="Characteristic">
                            <div class="input-group-append">
                                <button type="button" class="btn btn-danger remove-field">-</button>
                            </div>
                        </div>`;
        $('#characteristics-container').append(newField);
    });

    // Add new variant field
    $(document).on('click', '.add-variant', function() {
        var newVariant = `<div class="col-sm-10">
                            <div class="input-group mb-2">
                                <select class="form-control" name="variable_duration[]">
                                    <option value="monthly">Monthly</option>
                                    <option value="yearly">Yearly</option>
                                </select>
                                <input type="number" step="0.01" class="form-control" name="variable_price[]" placeholder="Price">
                                <div class="input-group-append">
                                    <button type="button" class="btn btn-danger remove-field">-</button>
                                </div>
                            </div>
                        </div>`;
        $('#variant-container').append(newVariant);
    });

    // Remove field
    $(document).on('click', '.remove-field', function() {
        $(this).closest('.input-group').remove();
    });
});
</script>
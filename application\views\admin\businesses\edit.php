<!-- Start content -->
<div class="content">

    <div class="container-fluid">
        <div class="page-title-box">

            <div class="row align-items-center ">
                <div class="col-md-12">
                    <div class="page-title-box">
                        <h4 class="page-title">Package Edit</h4>

                    </div>
                </div>
            </div>
        </div>
        <!-- end page-title -->

        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <form method='POST' enctype="multipart/form-data"
                            action="<?= site_url(); ?>master/packages/update">
                            <input type="hidden" name="package_id" value="<?= $package->id; ?>">

                            <?php if ($this->session->flashdata('package-update')) { ?>
                            <div class="alert alert-success mb-2"><?= $this->session->flashdata("package-update"); ?>
                            </div>
                            <?php } ?>

                            <?php if ($this->session->flashdata('package-error')) { ?>
                            <div class="alert alert-danger mb-2"><?= $this->session->flashdata("package-error"); ?>
                            </div>
                            <?php } ?>

                            <div class="form-group row">
                                <label class="col-sm-2 col-form-label" for="package-title">Package Title</label>
                                <div class="col-sm-10">
                                    <input type="text" class="form-control" id="package-title" name="package_title"
                                        value="<?= $package->title; ?>" required>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label class="col-sm-2 col-form-label" for="package-type">Package Type</label>
                                <div class="col-sm-10">
                                    <select class="form-control" id="package-type" name="package_type" required>
                                        <option value="free" <?= $package->type === 'free' ? 'selected' : ''; ?>>Free
                                        </option>
                                        <option value="paid" <?= $package->type === 'paid' ? 'selected' : ''; ?>>Paid
                                        </option>
                                    </select>
                                </div>
                            </div>

                            <!-- Paid Package Options -->
                            <div id="paid-package-options" class="<?= $package->type === 'paid' ? '' : 'd-none'; ?>">
                                <div class="form-group row">
                                    <label class="col-sm-2 col-form-label" for="paid-package-type">Paid Package
                                        Type</label>
                                    <div class="col-sm-10">
                                        <select class="form-control" id="paid-package-type" name="paid_package_type">
                                            <option value="single"
                                                <?= $package->paid_type === 'single' ? 'selected' : ''; ?>>Single
                                            </option>
                                            <option value="variable"
                                                <?= $package->paid_type === 'variable' ? 'selected' : ''; ?>>Variable
                                            </option>
                                        </select>
                                    </div>
                                </div>

                                <!-- Single Option -->
                                <div id="single-options"
                                    class="<?= $package->paid_type === 'single' ? '' : 'd-none'; ?>">
                                    <div class="form-group row">
                                        <label class="col-sm-2 col-form-label" for="single-duration">Duration</label>
                                        <div class="col-sm-10">
                                            <select class="form-control" id="single-duration" name="single_duration">
                                                <option value="monthly"
                                                    <?= $package->duration === 'monthly' ? 'selected' : ''; ?>>Monthly
                                                </option>
                                                <option value="yearly"
                                                    <?= $package->duration === 'yearly' ? 'selected' : ''; ?>>Yearly
                                                </option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-sm-2 col-form-label" for="single-price">Price</label>
                                        <div class="col-sm-10">
                                            <input type="number" step="0.01" class="form-control" id="single-price"
                                                name="single_price" value="<?= $package->price; ?>" placeholder="Price">
                                        </div>
                                    </div>
                                </div>

                                <!-- Variable Option -->
                                <div id="variable-options"
                                    class="<?= $package->paid_type === 'variable' ? '' : 'd-none'; ?>">
                                    <div id="variant-container">
                                        <?php foreach ($package->variants as $variant) { ?>
                                        <div class="form-group row">
                                            <label class="col-sm-2 col-form-label">Variants</label>
                                            <div class="col-sm-10">
                                                <div class="input-group mb-2">
                                                    <select class="form-control" name="variable_duration[]">
                                                        <option value="monthly"
                                                            <?= $variant['duration'] === 'monthly' ? 'selected' : ''; ?>>
                                                            Monthly</option>
                                                        <option value="yearly"
                                                            <?= $variant['duration'] === 'yearly' ? 'selected' : ''; ?>>
                                                            Yearly</option>
                                                    </select>
                                                    <input type="number" step="0.01" class="form-control"
                                                        name="variable_price[]" value="<?= $variant['price']; ?>"
                                                        placeholder="Price">
                                                    <div class="input-group-append">
                                                        <button type="button"
                                                            class="btn btn-danger remove-field">-</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <?php } ?>
                                    </div>
                                </div>
                            </div>

                            <!-- Characteristics Section -->
                            <div class="form-group row">
                                <label class="col-sm-2 col-form-label">Characteristics</label>
                                <div class="col-sm-10">
                                    <div id="characteristics-container">
                                        <?php 
                                        $characteristics = json_decode($package->characteristics);
                                        foreach ($characteristics as $characteristic) { ?>
                                        <div class="input-group mb-2">
                                            <input type="text" class="form-control" name="characteristics[]"
                                                value="<?= $characteristic; ?>" placeholder="Characteristic">
                                            <div class="input-group-append">
                                                <button type="button" class="btn btn-danger remove-field">-</button>
                                            </div>
                                        </div>
                                        <?php } ?>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group mb-0 text-center">
                                <button type="submit" class="btn btn-primary waves-effect waves-light">Update</button>
                                <button type="reset" class="btn btn-secondary waves-effect m-l-5">Reset</button>
                            </div>
                        </form>


                    </div>
                </div>
            </div>
            <!-- end col -->
        </div>
        <!-- end row -->

    </div>
    <!-- container-fluid -->

</div>
<!-- content -->

<script>
$(document).ready(function() {
    $('#package-type').change(function() {
        if ($(this).val() === 'free') {
            $('#paid-package-options').addClass('d-none');
        } else {
            $('#paid-package-options').removeClass('d-none');
        }
    });

    $('#paid-package-type').change(function() {
        if ($(this).val() === 'single') {
            $('#single-options').removeClass('d-none');
            $('#variable-options').addClass('d-none');
        } else {
            $('#variable-options').removeClass('d-none');
            $('#single-options').addClass('d-none');
        }
    });

    $(document).on('click', '.add-characteristic', function() {
        var newField = `<div class="input-group mb-2">
                            <input type="text" class="form-control" name="characteristics[]" placeholder="Characteristic">
                            <div class="input-group-append">
                                <button type="button" class="btn btn-danger remove-field">-</button>
                            </div>
                        </div>`;
        $('#characteristics-container').append(newField);
    });

    $(document).on('click', '.add-variant', function() {
        var newVariant = `<div class="form-group row">
                            <label class="col-sm-2 col-form-label">Variants</label>
                            <div class="col-sm-10">
                                <div class="input-group mb-2">
                                    <select class="form-control" name="variable_duration[]">
                                        <option value="monthly">Monthly</option>
                                        <option value="yearly">Yearly</option>
                                    </select>
                                    <input type="number" step="0.01" class="form-control" name="variable_price[]" placeholder="Price">
                                    <div class="input-group-append">
                                        <button type="button" class="btn btn-danger remove-field">-</button>
                                    </div>
                                </div>
                            </div>
                        </div>`;
        $('#variant-container').append(newVariant);
    });

    $(document).on('click', '.remove-field', function() {
        $(this).closest('.input-group').remove();
    });
});

</script>
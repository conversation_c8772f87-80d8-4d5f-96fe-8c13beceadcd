<!-- Start content -->
<div class="content">

    <div class="container-fluid">
        <div class="page-title-box">

            <div class="row align-items-center ">
                <div class="col-md-8">
                    <div class="page-title-box">
                        <h4 class="page-title">Commission's Record</h4>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="page-title-box text-right">
                        <a href="<?= site_url('master/commission/add') ?>" class="btn btn-success mb-3">Add New</a>
                    </div>
                </div>
            </div>
        </div>
        <!-- end page-title -->

        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="table-responsive">
                            <?php if (count($commissions) > 0) { ?>
                                <table id="datatable-buttons" class="table table-bordered table-striped dt-responsive nowrap"
                                       style="border-collapse: collapse; border-spacing: 0; width: 100%;">
                                    <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Title</th>
                                        <th>Percentage (%)</th>
                                        <th>Actions</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <?php foreach ($commissions as $row): ?>
                                        <tr>
                                            <td><?= $row->id ?></td>
                                            <td><?= htmlspecialchars($row->title) ?></td>
                                            <td><?= number_format($row->percentage, 2) ?></td>
                                            <td>
                                                <a href="<?= site_url('admin/commission/edit/' . $row->id) ?>"
                                                   class="btn btn-sm btn-info">Edit</a>
                                                <a href="<?= site_url('admin/commission/delete/' . $row->id) ?>"
                                                   class="btn btn-sm btn-danger"
                                                   onclick="return confirm('Delete this commission?')">Delete</a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                    </tbody>
                                </table>
                            <?php } else { ?>
                                <div class="alert alert-danger wow fadeInUp" role="alert">No Data Found!</div>
                            <?php } ?>
                        </div>
                    </div>
                </div>
            </div>
            <!-- end col -->
        </div>
        <!-- end row -->

    </div>
    <!-- container-fluid -->

</div>
<!-- content -->

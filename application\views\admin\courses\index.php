 <!-- Start content -->
 <div class="content">

     <div class="container-fluid">
         <div class="page-title-box">

             <div class="row align-items-center ">
                 <div class="col-md-12">
                     <div class="page-title-box">
                         <h4 class="page-title">Course's Record</h4>

                     </div>
                 </div>





             </div>
         </div>
         <!-- end page-title -->

         <div class="row">
             <div class="col-12">
                 <div class="card">
                     <div class="card-body">
                         <div class="table-responsive">
                             <?php if(count($courses) > 0) { ?>
                             <table id="datatable-buttons"
                                 class="table table-bordered table-striped dt-responsive nowrap"
                                 style="border-collapse: collapse; border-spacing: 0; width: 100%;">
                                 <thead>
                                     <tr>
                                         <th>ID</th>
                                         <th>Banner</th>
                                         <th>Title</th>
                                         <th>Description</th>
                                         <th>Language</th>
                                         <th>Status</th>
                                         <th>Action</th>
                                     </tr>
                                 </thead>

                                 <tbody>
                                     <?php foreach ($courses as $course) { ?>
                                     <tr data-id="<?= $course->id ?>" data-status="<?= $course->status ?>">
                                         <td><?= $course->id ?></td>
                                         <td>
                                             <img src="<?= base_url('assets/course_thumbnail/' . $course->thumbnail) ?>"
                                                 style="width:150px; height:100px;" alt="Banner">
                                         </td>
                                         <td><?= $course->title ?></td>
                                         <td><?= word_limiter(strip_tags($course->description), 20) ?></td>
                                         <td><?= $course->language ?></td>
                                         <td>
                                             <?php
                        if ($course->status == 'pending') echo '<span class="badge badge-warning">Pending</span>';
                        elseif ($course->status == 'approved') echo '<span class="badge badge-success">Approved</span>';
                        else echo '<span class="badge badge-danger">Disapproved</span>';
                    ?>
                                         </td>
                                         <td>
                                             <div class="btn-group">
                                                 <button class="btn btn-sm btn-primary dropdown-toggle" type="button"
                                                     data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                     Action
                                                 </button>
                                                 <div class="dropdown-menu">
                                                     <a href="javascript:void(0);" class="dropdown-item update-status"
                                                         data-id="<?= $course->id ?>" data-status="approved"
                                                         data-action="approve">
                                                         Approve
                                                     </a>
                                                     <a href="javascript:void(0);" class="dropdown-item update-status"
                                                         data-id="<?= $course->id ?>" data-status="disapproved"
                                                         data-action="disapprove">
                                                         Disapprove
                                                     </a>
                                                     <a href="<?= site_url('course/details/' . $course->id) ?>"
                                                         class="dropdown-item" target="_blank">
                                                         Preview
                                                     </a>
                                                 </div>
                                             </div>
                                         </td>
                                     </tr>
                                     <?php } ?>
                                 </tbody>
                             </table>
                             <?php } else { ?>
                             <div class="alert alert-danger">No Courses Found!</div>
                             <?php } ?>
                         </div>


                     </div>
                 </div>
             </div>
             <!-- end col -->
         </div>
         <!-- end row -->



     </div>
     <!-- container-fluid -->

 </div>
 <!-- content -->

 <script>
$(document).on('click', '.update-status', function () {
    var courseId = $(this).data('id');
    var newStatus = $(this).data('status');
    var action = $(this).data('action');

    Swal.fire({
        title: 'Are you sure?',
        text: "You want to " + action + " this course?",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#28a745',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, ' + action + ' it!'
    }).then((result) => {
        if (result.isConfirmed) {
            $.post("<?= base_url('admin/courses/update_status') ?>", {
                id: courseId,
                status: newStatus
            }, function (response) {
                let res = JSON.parse(response);
                if (res.status) {
                    Swal.fire('Updated!', res.message, 'success').then(() => {
                        location.reload();
                    });
                } else {
                    Swal.fire('Error!', res.message, 'error');
                }
            });
        }
    });
});
</script>

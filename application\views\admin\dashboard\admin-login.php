<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0, minimal-ui">
    <title>Deal Closed | Login</title>
    <link rel="shortcut icon" href="<?=base_url();?>assets/admin/images/favicon.ico">
    <link href="<?=base_url();?>assets/admin/css/bootstrap.min.css" rel="stylesheet" type="text/css">
    <link href="<?=base_url();?>assets/admin/css/metismenu.min.css" rel="stylesheet" type="text/css">
    <link href="<?=base_url();?>assets/admin/css/icons.css" rel="stylesheet" type="text/css">
    <link href="<?=base_url();?>assets/admin/css/style.css" rel="stylesheet" type="text/css">

</head>

<body>
    <div class="accountbg"></div>


    <div class="wrapper-page">

        <div class="container">
            <div class="row align-items-center justify-content-center">
                <div class="col-lg-5">
                    <div class="card card-pages shadow-none mt-4">
                        <div class="card-body">
                            <div class="text-center mt-0 mb-3">
                                <a href="" class="logo logo-admin">
                                    <img src="<?=base_url();?>assets/admin/images/logo.png" class="mt-3" alt="" height="26"></a>
                                <p class="text-muted w-75 mx-auto mb-4 mt-4">Enter your username and password to
                                    access dashboard panel.</p>
                            </div>
                            <?php

                            if($this->session->flashdata('login-error'))
                        {
                            echo '
                            <div class="alert alert-danger mb-2">
                            '.$this->session->flashdata("login-error").'
                            </div>
                            ';
                        }
                        ?>

                            <form class="form-horizontal mt-4" method="POST"
                                action="<?=site_url();?>admin/login/can_login">

                                <div class="form-group">
                                    <div class="col-12">
                                        <label for="username">Username</label>
                                        <input class="form-control" name='username' value="<?=isset($_COOKIE['smart_admin_username']) ? $_COOKIE['smart_admin_username'] : '';?>" type="text" required="" id="username"
                                            placeholder="Username">
                                    </div>
                                </div>

                                <div class="form-group">
                                    <div class="col-12">
                                        <label for="password">Password</label>
                                        <input class="form-control" name='password' type="password" value="<?=isset($_COOKIE['kmap_admin_password']) ? $_COOKIE['kmap_admin_password'] : '';?>" required="" id="password"
                                            placeholder="Password">
                                    </div>
                                </div>

                                <div class="form-group">
                                    <div class="col-12">
                                        <div class="checkbox checkbox-primary">
                                            <div class="custom-control custom-checkbox">
                                                <input type="checkbox" name='remember' <?=isset($_COOKIE['kmap_admin_username']) ? 'checked' : '';?> class="custom-control-input" id="customCheck1">
                                                <label class="custom-control-label" for="customCheck1"> Remember
                                                    me</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group text-center mt-3">
                                    <div class="col-12">
                                        <button class="btn btn-primary btn-block waves-effect waves-light"
                                            type="submit">Log In</button>
                                    </div>
                                </div>
                            </form>

                        </div>

                    </div>

                </div>
            </div>
            <!-- end row -->
        </div>
    </div>

    <!-- jQuery  -->
    <script src="<?=base_url();?>assets/admin/js/jquery.min.js"></script>
    <script src="<?=base_url();?>assets/admin/js/bootstrap.bundle.min.js"></script>
    <script src="<?=base_url();?>assets/admin/js/metismenu.min.js"></script>
    <script src="<?=base_url();?>assets/admin/js/jquery.slimscroll.js"></script>
    <script src="<?=base_url();?>assets/admin/js/waves.min.js"></script>
    <!-- App js -->
    <script src="<?=base_url();?>assets/admin/js/app.js"></script>

</body>

</html>
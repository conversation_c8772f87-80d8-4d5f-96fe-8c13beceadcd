 <!-- Start content -->
 <div class="content">

     <div class="container-fluid">
         <div class="page-title-box">

             <div class="row align-items-center ">
                 <div class="col-md-8">
                     <div class="page-title-box">
                         <h4 class="page-title">Promotion's Record</h4>

                     </div>
                 </div>

                 <div class="col-md-4">
                     <div class="page-title-box text-right">
                         <a href="<?= site_url('master/promotion-settings/add') ?>" class="btn btn-success mb-3">Add
                             New</a>

                     </div>
                 </div>



             </div>
         </div>
         <!-- end page-title -->

         <div class="row">
             <div class="col-12">
                 <div class="card">
                     <div class="card-body">
                         <div class="table-responsive">

                             <?php if(count($settings) > 0) {?>


                             <table id="datatable-buttons"
                                 class="table table-bordered table-striped dt-responsive nowrap"
                                 style="border-collapse: collapse; border-spacing: 0; width: 100%;">
                                 <thead>
                                     <tr>
                                         <th>ID</th>
                                         <th>Type</th>
                                         <th>Duration (days)</th>
                                         <th>Price ($)</th>
                                         <th>Actions</th>
                                     </tr>

                                 </thead>
                                 <tbody>
                                     <?php foreach ($settings as $row): ?>
                                     <tr>
                                         <td><?= $row->id ?></td>
                                         <td><?= ucfirst(str_replace('_', ' ', $row->type)) ?></td>
                                         <td><?= $row->duration_days ?></td>
                                         <td><?= $row->price ?></td>
                                         <td>
                                             <a href="<?= site_url('master/PromotionSettings/edit/'.$row->id) ?>"
                                                 class="btn btn-sm btn-info">Edit</a>
                                             <a href="<?= site_url('master/PromotionSettings/delete/'.$row->id) ?>"
                                                 class="btn btn-sm btn-danger"
                                                 onclick="return confirm('Delete this setting?')">Delete</a>
                                         </td>
                                     </tr>
                                     <?php endforeach; ?>
                                 </tbody>
                             </table>
                         </div>
                         <?php
                    }else{
                    ?>
                         <div class="alert alert-danger wow fadeInUp" role="alert"> No Data Found! </div>
                         <?php
                    }
                    ?>
                     </div>
                 </div>
             </div>
             <!-- end col -->
         </div>
         <!-- end row -->



     </div>
     <!-- container-fluid -->

 </div>
 <!-- content -->
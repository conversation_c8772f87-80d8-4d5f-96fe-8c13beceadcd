<!-- Start content -->
<div class="content">

    <div class="container-fluid">
        <div class="page-title-box">

            <div class="row align-items-center ">
                <div class="col-md-12">
                    <div class="page-title-box">
                        <h4 class="page-title">Property Add</h4>

                    </div>
                </div>
            </div>
        </div>
        <!-- end page-title -->

        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <form method='POST' enctype="multipart/form-data"
                            action="<?=site_url();?>master/properties/save">
                            <?php

                                if($this->session->flashdata('property-add'))
                                {
                                echo '
                                <div class="alert alert-success mb-2">
                                '.$this->session->flashdata("property-add").'
                                </div>
                                ';
                                }
                            ?>

                            <?php

                                if($this->session->flashdata('property-error'))
                                {
                                echo '
                                <div class="alert alert-danger mb-2">
                                '.$this->session->flashdata("property-error").'
                                </div>
                                ';
                                }
                            ?>

                            <div class="form-group row">
                                <label class="col-sm-2 col-form-label">Type of Ads <sup class='text-danger'>*</sup></label>
                                <div class="col-sm-10">
                                    <select name="ads_type" class="form-control" required>
                                        
                                        <option selected value="admin">Admin</option>
                                        <option value="user">User</option>
                                        <option value="auction">Auction</option>
                                    </select>
                                </div>
                            </div>


                            <div class="form-group row">
                                <label class="col-sm-2 col-form-label">Title <sup class='text-danger'>*</sup></label>
                                <div class="col-sm-10">
                                    <input type="text" class="form-control" name="title" placeholder="Title" required>
                                </div>
                            </div>



                            <div class="variant-section">
                                <div class="form-group">
                                    <label>Plot Details</label>
                                    <div id="variant-container" class="mt-3">
                                        <div class="variant-group border p-3 rounded mb-3">
                                            <div class="form-row mb-3">
                                                <div class="col">
                                                    <label class="form-label">Administrative Unit <sup class='text-danger'>*</sup></label>
                                                    <select name="admin_unit[]" class="form-control admin_unit"
                                                        required>
                                                        <option disabled selected>Select Administrative Unit
                                                        </option>
                                                        <option value="Ajdovščina">Ajdovščina</option>
                                                        <option value="Beltinci">Beltinci</option>
                                                        <option value="Brežice">Brežice</option>
                                                        <option value="Celje">Celje</option>
                                                        <option value="Cerknica">Cerknica</option>
                                                        <option value="Črnomelj">Črnomelj</option>
                                                        <option value="Domžale">Domžale</option>
                                                        <option value="Dravograd">Dravograd</option>
                                                        <option value="Gornja Radgona">Gornja Radgona</option>
                                                        <option value="Grosuplje">Grosuplje</option>
                                                        <option value="Hrastnik">Hrastnik</option>
                                                        <option value="Idrija">Idrija</option>
                                                        <option value="Ilirska Bistrica">Ilirska Bistrica</option>
                                                        <option value="Izola">Izola</option>
                                                        <option value="Jesenice">Jesenice</option>
                                                        <option value="Kamnik">Kamnik</option>
                                                        <option value="Kočevje">Kočevje</option>
                                                        <option value="Koper">Koper</option>
                                                        <option value="Kranj">Kranj</option>
                                                        <option value="Krško">Krško</option>
                                                        <option value="Laško">Laško</option>
                                                        <option value="Lenart">Lenart</option>
                                                        <option value="Litija">Litija</option>
                                                        <option value="Ljubljana">Ljubljana</option>
                                                        <option value="Ljutomer">Ljutomer</option>
                                                        <option value="Logatec">Logatec</option>
                                                        <option value="Maribor">Maribor</option>
                                                        <option value="Metlika">Metlika</option>
                                                        <option value="Mozirje">Mozirje</option>
                                                        <option value="Murska Sobota">Murska Sobota</option>
                                                        <option value="Nova Gorica">Nova Gorica</option>
                                                        <option value="Novo mesto">Novo mesto</option>
                                                        <option value="Ormož">Ormož</option>
                                                        <option value="Pesnica">Pesnica</option>
                                                        <option value="Piran">Piran</option>
                                                        <option value="Postojna">Postojna</option>
                                                        <option value="Ptuj">Ptuj</option>
                                                        <option value="Radlje ob Dravi">Radlje ob Dravi</option>
                                                        <option value="Radovljica">Radovljica</option>
                                                        <option value="Ravne na Koroškem">Ravne na Koroškem</option>
                                                        <option value="Ribnica">Ribnica</option>
                                                        <option value="Ruše">Ruše</option>
                                                        <option value="Sevnica">Sevnica</option>
                                                        <option value="Sežana">Sežana</option>
                                                        <option value="Slovenj Gradec">Slovenj Gradec</option>
                                                        <option value="Slovenska Bistrica">Slovenska Bistrica</option>
                                                        <option value="Slovenske Konjice">Slovenske Konjice</option>
                                                        <option value="Šentjur">Šentjur</option>
                                                        <option value="Škofja Loka">Škofja Loka</option>
                                                        <option value="Tolmin">Tolmin</option>
                                                        <option value="Trbovlje">Trbovlje</option>
                                                        <option value="Trebnje">Trebnje</option>
                                                        <option value="Tržič">Tržič</option>
                                                        <option value="Velenje">Velenje</option>
                                                        <option value="Vrhnika">Vrhnika</option>
                                                        <option value="Zagorje ob Savi">Zagorje ob Savi</option>
                                                        <option value="Zalec">Zalec</option>
                                                        <option value="Ziri">Ziri</option>
                                                    </select>
                                                </div>
                                                <div class="col">
                                                    <label class="form-label">Cadastral Number <sup class='text-danger'>*</sup></label>
                                                    <select name='cadastral_number[]'
                                                        class="form-control cadastral_number" required>
                                                        <option selected disabled>Select Cadastral Number</option>
                                                        <?php
                                                        foreach($cadastral_numbers as $cadastral_number){
                                                        ?>
                                                        <option value="<?=$cadastral_number['cadastral_number']?>">
                                                            <?=$cadastral_number['name']?></option>
                                                        <?php
                                                        }
                                                        ?>
                                                    </select>
                                                </div>
                                                <div class="col">
                                                    <label class="form-label">Plot Number</label>
                                                    <input type="text" class="form-control" name="plot_number[]"
                                                        placeholder="Plot Number">
                                                </div>
                                                <div class="col">
                                                    <label class="form-label">Plot Type <sup class='text-danger'>*</sup></label>
                                                    <select id="plotType" name="plot_type" class="form-control"
                                                        required>
                                                        <option disabled selected>Select Plot Type</option>
                                                        <option value="meadow">Meadow</option>
                                                        <option value="field">Field</option>
                                                        <option value="forest">Forest</option>
                                                        <option value="building land">Building Land</option>
                                                        <option value="building on land">Building On Land</option>
                                                        <option value="other">Other</option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="form-row mb-3">
                                                <div class="col">
                                                    <label class="form-label">Building Exist</label>
                                                    <input type="checkbox" class="d-block mt-3" name="building_exists[]"
                                                        value="1">

                                                </div>
                                                <div class="col">
                                                    <label class="form-label">Latitude <sup class='text-danger'>*</sup><button
                                                            class='badge bg-success getLocationBtn'>Get Current
                                                            Locations</button></label>
                                                    <input type="text" class="form-control latitude" name="latitude[]"
                                                        placeholder="Latitude" required>
                                                </div>
                                                <div class="col">
                                                    <label class="form-label">Longitude <sup class='text-danger'>*</sup></label>
                                                    <input type="text" class="form-control longitude" name="longitude[]"
                                                        placeholder="Longitude" required>

                                                </div>
                                                <div class="col">
                                                    <label class="form-label">Address</label>
                                                    <input type="text" class="form-control" name="address[]"
                                                        placeholder="Address" required>

                                                </div>
                                            </div>

                                            <div class="form-row mb-3">
                                                <div class="col">
                                                    <label class="form-label">Land Leased</label>
                                                    <input class="d-block mt-3" type="checkbox" name="land_leased[]"
                                                        value="1">

                                                </div>
                                                <div class="col">
                                                    <label class="form-label">Land Buildable</label>

                                                    <input class="d-block mt-3" type="checkbox" name="land_buildable[]"
                                                        value="1">


                                                </div>
                                                <div class="col">
                                                    <label class="form-label">Price per sqm</label>

                                                    <input type="number" class="form-control" name="price_per_sqm[]"
                                                        placeholder="Price per sqm">

                                                </div>

                                                <div class="col">

                                                    <label class="form-label">Square Meters</label>

                                                    <input type="number" class="form-control" name="square_meters"
                                                        placeholder="Square Meters">


                                                </div>

                                            </div>

                                            <div class="form-row mb-3">
                                                <div class="col">
                                                    <label class="form-label">Package Sale</label>
                                                    <input type="checkbox" class="d-block mt-2" name="package_sale[]"
                                                        value="1">

                                                </div>



                                                <div class="col">
                                                    <label class="form-label">Final Price</label>
                                                    <input type="number" class="form-control" name="final_price[]"
                                                        placeholder="Final Price">
                                                </div>

                                                <div class="col">
                                                    <label class="form-label">Start Date <sup class='text-danger'>*</sup></label>
                                                    <input type="date" class="form-control" name="start_date[]" required>
                                                </div>

                                                <div class="col">
                                                    <label class="form-label">Expiration Date</label>
                                                    <input type="date" class="form-control" name="expiration_date[]">
                                                </div>

                                            </div>

                                            <div class="form-row mb-3">


                                                <div class="col-6">
                                                    <label class="form-label">Auction Date</label>
                                                    <input type="date" class="form-control" name="auction_date[]">
                                                </div>
                                                <div class="col-3">
                                                    <label class="form-label">Share in Real Estate(%)</label>
                                                    <input type="number" class="form-control" name="share[]">
                                                </div>

                                                <div class="col-3 mt-4 text-right">
                                                    <button type="button" class="btn btn-success add-variant">Add New Property</button>
                                                </div>
                                            </div>

                                        </div>
                                    </div>
                                </div>
                            </div>




                            <div class="form-group row mt-4">
                                <label class="col-sm-2 col-form-label">Photos</label>
                                <div class="col-sm-10">
                                    <input type="file" class="form-control" name="photos[]" multiple>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label class="col-sm-2 col-form-label">Description <sup class='text-danger'>*</sup></label>
                                <div class="col-sm-10">
                                    <textarea class="form-control" name="description" placeholder="Description"
                                        required></textarea>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label class="col-sm-2 col-form-label">Document Link</label>
                                <div class="col-sm-10">
                                    <input type="url" class="form-control" name="document_link"
                                        placeholder="Document Link">
                                </div>
                            </div>

                            <div class="form-group row">
                                <label class="col-sm-2 col-form-label">Rent or Sell <sup class='text-danger'>*</sup></label>
                                <div class="col-sm-10">
                                    <select class="form-control" name="rent_or_sell" required>
                                        <option selected value="rent">Rent</option>
                                        <option value="sell">Sell</option>
                                    </select>
                                </div>
                            </div>

                            <div class="form-group row">
                                <div id="map" style="height: 400px; width: 100%;"></div>
                            </div>



                            <div class="form-group mb-0">
                                <div class='text-center'>
                                    <button type="submit" class="btn btn-primary waves-effect waves-light">
                                        Save
                                    </button>
                                    <button type="reset" class="btn btn-secondary waves-effect m-l-5">
                                        Reset
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <!-- end col -->
        </div>
        <!-- end row -->

    </div>
    <!-- container-fluid -->

</div>
<!-- content -->
<script>
$(document).ready(function() {
    $('.admin_unit').select2({
        minimumResultsForSearch: 10 // at least 20 results must be displayed
    });

    $('.cadastral_number').select2({
        minimumResultsForSearch: 10 // at least 20 results must be displayed
    });
   
});
</script>


<script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyAiIwP-cOFq4mX7NHh0mIvkp4bhT3zj4KM"></script>

<script>
let map, marker;

function initMap() {
    // Initialize the map centered at a default location
    const defaultLocation = {
        lat: 46.119944,
        lng: 14.815333
    }; // Default: Dubai coordinates
    map = new google.maps.Map(document.getElementById("map"), {
        center: defaultLocation,
        zoom: 10,
    });

    // Add a marker on the map
    marker = new google.maps.Marker({
        position: defaultLocation,
        map: map,
        draggable: true,
    });

    // Update latitude and longitude when the marker is dragged
    marker.addListener("dragend", () => {
        const position = marker.getPosition();
        $("input.latitude").last().val(position.lat());
        $("input.longitude").last().val(position.lng());
    });

    // Update latitude and longitude when clicking on the map
    map.addListener("click", (event) => {
        const clickedLat = event.latLng.lat();
        const clickedLng = event.latLng.lng();

        // Set the marker to the clicked location
        marker.setPosition({
            lat: clickedLat,
            lng: clickedLng
        });

        // Update input fields
        $("input.latitude").last().val(clickedLat);
        $("input.longitude").last().val(clickedLng);
    });
}

// Add a click event to the "Get Location" button
$(document).on("click", ".getLocationBtn", function() {
    const closestRow = $(this).closest(".row"); // Find the closest row that contains the inputs

    if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(
            (position) => {
                const lat = position.coords.latitude;
                const lng = position.coords.longitude;

                // Update the map and marker
                const currentLocation = {
                    lat: lat,
                    lng: lng
                };
                map.setCenter(currentLocation);
                marker.setPosition(currentLocation);

                // Update input fields inside the closest row
                closestRow.find(".latitude").val(lat);
                closestRow.find(".longitude").val(lng);
            },
            () => {
                alert("Unable to retrieve your location.");
            }
        );
    } else {
        alert("Geolocation is not supported by this browser.");
    }
});
$(document).ready(function() {
    initMap();
});
</script>

<script>
$(document).on('click', '.add-variant', function() {

    let variant_group = `
    <div class="variant-group border p-3 rounded mb-3">
                                            <div class="form-row mb-3">
                                                <div class="col">
                                                    <label class="form-label">Administrative Unit</label>
                                                    <select name="admin_unit[]" class="form-control admin_unit"
                                                        required>
                                                        <option value="" disabled selected>Select Administrative Unit
                                                        </option>
                                                        <option value="Ajdovščina">Ajdovščina</option>
                                                        <option value="Beltinci">Beltinci</option>
                                                        <option value="Brežice">Brežice</option>
                                                        <option value="Celje">Celje</option>
                                                        <option value="Cerknica">Cerknica</option>
                                                        <option value="Črnomelj">Črnomelj</option>
                                                        <option value="Domžale">Domžale</option>
                                                        <option value="Dravograd">Dravograd</option>
                                                        <option value="Gornja Radgona">Gornja Radgona</option>
                                                        <option value="Grosuplje">Grosuplje</option>
                                                        <option value="Hrastnik">Hrastnik</option>
                                                        <option value="Idrija">Idrija</option>
                                                        <option value="Ilirska Bistrica">Ilirska Bistrica</option>
                                                        <option value="Izola">Izola</option>
                                                        <option value="Jesenice">Jesenice</option>
                                                        <option value="Kamnik">Kamnik</option>
                                                        <option value="Kočevje">Kočevje</option>
                                                        <option value="Koper">Koper</option>
                                                        <option value="Kranj">Kranj</option>
                                                        <option value="Krško">Krško</option>
                                                        <option value="Laško">Laško</option>
                                                        <option value="Lenart">Lenart</option>
                                                        <option value="Litija">Litija</option>
                                                        <option value="Ljubljana">Ljubljana</option>
                                                        <option value="Ljutomer">Ljutomer</option>
                                                        <option value="Logatec">Logatec</option>
                                                        <option value="Maribor">Maribor</option>
                                                        <option value="Metlika">Metlika</option>
                                                        <option value="Mozirje">Mozirje</option>
                                                        <option value="Murska Sobota">Murska Sobota</option>
                                                        <option value="Nova Gorica">Nova Gorica</option>
                                                        <option value="Novo mesto">Novo mesto</option>
                                                        <option value="Ormož">Ormož</option>
                                                        <option value="Pesnica">Pesnica</option>
                                                        <option value="Piran">Piran</option>
                                                        <option value="Postojna">Postojna</option>
                                                        <option value="Ptuj">Ptuj</option>
                                                        <option value="Radlje ob Dravi">Radlje ob Dravi</option>
                                                        <option value="Radovljica">Radovljica</option>
                                                        <option value="Ravne na Koroškem">Ravne na Koroškem</option>
                                                        <option value="Ribnica">Ribnica</option>
                                                        <option value="Ruše">Ruše</option>
                                                        <option value="Sevnica">Sevnica</option>
                                                        <option value="Sežana">Sežana</option>
                                                        <option value="Slovenj Gradec">Slovenj Gradec</option>
                                                        <option value="Slovenska Bistrica">Slovenska Bistrica</option>
                                                        <option value="Slovenske Konjice">Slovenske Konjice</option>
                                                        <option value="Šentjur">Šentjur</option>
                                                        <option value="Škofja Loka">Škofja Loka</option>
                                                        <option value="Tolmin">Tolmin</option>
                                                        <option value="Trbovlje">Trbovlje</option>
                                                        <option value="Trebnje">Trebnje</option>
                                                        <option value="Tržič">Tržič</option>
                                                        <option value="Velenje">Velenje</option>
                                                        <option value="Vrhnika">Vrhnika</option>
                                                        <option value="Zagorje ob Savi">Zagorje ob Savi</option>
                                                        <option value="Zalec">Zalec</option>
                                                        <option value="Ziri">Ziri</option>
                                                    </select>
                                                </div>
                                                <div class="col">
                                                    <label class="form-label">Cadastral Number</label>
                                                    <select name='cadastral_number[]'
                                                        class="form-control cadastral_number" required>
                                                        <option value="">Select Cadastral Number</option>
                                                        <?php
                                                        foreach($cadastral_numbers as $cadastral_number){
                                                        ?>
                                                        <option value="<?=$cadastral_number['cadastral_number']?>">
                                                            <?=$cadastral_number['name']?></option>
                                                        <?php
                                                        }
                                                        ?>
                                                    </select>
                                                </div>
                                                <div class="col">
                                                    <label class="form-label">Plot Number</label>
                                                    <input type="text" class="form-control" name="plot_number[]"
                                                        placeholder="Plot Number">
                                                </div>
                                                <div class="col">
                                                    <label class="form-label">Plot Type</label>
                                                    <select id="plotType" name="plot_type" class="form-control"
                                                        required>
                                                        <option value="" disabled selected>Select Plot Type</option>
                                                        <option value="meadow">Meadow</option>
                                                        <option value="field">Field</option>
                                                        <option value="forest">Forest</option>
                                                        <option value="building land">Building Land</option>
                                                        <option value="building on land">Building On Land</option>
                                                        <option value="other">Other</option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="form-row mb-3">
                                                <div class="col">
                                                    <label class="form-label">Building Exist</label>
                                                    <input type="checkbox" class="d-block mt-3" name="building_exists[]"
                                                        value="1">

                                                </div>
                                                <div class="col">
                                                    <label class="form-label">Latitude <button
                                                            class='badge bg-success getLocationBtn'>Get Current
                                                            Locations</button></label>
                                                    <input type="text" class="form-control latitude" name="latitude[]"
                                                        placeholder="Latitude" required>
                                                </div>
                                                <div class="col">
                                                    <label class="form-label">Longitude</label>
                                                    <input type="text" class="form-control longitude" name="longitude[]"
                                                        placeholder="Longitude" required>

                                                </div>
                                                <div class="col">
                                                    <label class="form-label">Address</label>
                                                    <input type="text" class="form-control" name="address[]"
                                                        placeholder="Address" required>

                                                </div>
                                            </div>

                                            <div class="form-row mb-3">
                                                <div class="col">
                                                    <label class="form-label">Land Leased</label>
                                                    <input class="d-block mt-3" type="checkbox" name="land_leased[]"
                                                        value="1">

                                                </div>
                                                <div class="col">
                                                    <label class="form-label">Land Buildable</label>

                                                    <input class="d-block mt-3" type="checkbox" name="land_buildable[]"
                                                        value="1">


                                                </div>
                                                <div class="col">
                                                    <label class="form-label">Price per sqm</label>

                                                    <input type="number" class="form-control" name="price_per_sqm[]"
                                                        placeholder="Price per sqm">

                                                </div>

                                                <div class="col">

                                                    <label class="form-label">Square Meters</label>

                                                    <input type="number" class="form-control" name="square_meters"
                                                        placeholder="Square Meters">


                                                </div>

                                            </div>

                                            <div class="form-row mb-3">
                                                <div class="col">
                                                    <label class="form-label">Package Sale</label>
                                                    <input type="checkbox" class="d-block mt-2" name="package_sale[]"
                                                        value="1">

                                                </div>



                                                <div class="col">
                                                    <label class="form-label">Final Price</label>
                                                    <input type="number" class="form-control" name="final_price[]"
                                                        placeholder="Final Price">
                                                </div>

                                                <div class="col">
                                                    <label class="form-label">Start Date</label>
                                                    <input type="date" class="form-control" name="start_date[]">
                                                </div>

                                                <div class="col">
                                                    <label class="form-label">Expiration Date</label>
                                                    <input type="date" class="form-control" name="expiration_date[]">
                                                </div>

                                            </div>

                                            <div class="form-row mb-3">


                                                <div class="col-6">
                                                    <label class="form-label">Auction Date</label>
                                                    <input type="date" class="form-control" name="auction_date[]">
                                                </div>

                                                <div class="col-3">
                                                    <label class="form-label">Share in Real Estate(%)</label>
                                                    <input type="number" class="form-control" name="share[]">
                                                </div>

                                                <div class="col-3 mt-4 text-right">
                                                    <button type="button" class="btn btn-danger remove-variant">Remove Property</button>
                                                </div>
                                            </div>

                                        </div>
    
    `;
    var variantContainer = $('#variant-container');
        variantContainer.append(variant_group);
        variantContainer.find('.admin_unit, .cadastral_number').select2({
    });
});

$(document).on('click', '.remove-variant', function() {
    $(this).closest('.variant-group').remove();
});

</script>
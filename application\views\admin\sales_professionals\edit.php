<!-- Start content -->

<div class="content">

    <div class="container-fluid">
        <div class="page-title-box">

            <div class="row align-items-center ">
                <div class="col-md-12">
                    <div class="page-title-box">
                        <h4 class="page-title">Property Edit</h4>

                    </div>
                </div>
            </div>
        </div>
        <!-- end page-title -->

        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <form action="<?= site_url('master/properties/edit/'.$property['id']); ?>" method="post"
                            enctype="multipart/form-data">

                            <div class="form-group row">
                                <label class="col-sm-2 col-form-label">Type of Ads <sup class='text-danger'>*</sup></label>
                                <div class="col-sm-10">
                                    <select id="administrativeUnit" name="ads_type" class="form-control" required>
                                       
                                        <option value="admin" <?= $property['ads_type'] == 'admin' ? 'selected' : '' ?>>
                                            Admin
                                        </option>
                                        <option value="user" <?= $property['ads_type'] == 'user' ? 'selected' : '' ?>>
                                            User</option>
                                        <option value="auction"
                                            <?= $property['ads_type'] == 'auction' ? 'selected' : '' ?>>Auction
                                        </option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-sm-2 col-form-label">Title <sup class='text-danger'>*</sup></label>
                                <div class="col-sm-10">
                                    <input type="text" class="form-control" name="title"
                                        value="<?= htmlspecialchars($property['title']); ?>" required>
                                </div>
                            </div>


                            <!-- Variant Section -->
                            <div class="variant-section">
                                <div class="form-group">
                                    <label>Plot Details</label>
                                    <div id="variant-container" class="mt-3">
                                        <?php $plots = json_decode($property['plot_details'], true); $index=0;  foreach ($plots as $plot): ?>
                                        <div class="variant-group border p-3 rounded mb-3">
                                            <div class="form-row mb-3">
                                                <div class="col">
                                                    <label class="form-label">Administrative Unit <sup class='text-danger'>*</sup></label>
                                                    <select id="administrativeUnit" name="admin_unit[]"
                                                        class="form-control admin_unit" required>
                                                       
                                                        <option value="Ajdovščina"
                                                            <?= $plot['admin_unit'] == 'Ajdovščina' ? 'selected' : '' ?>>
                                                            Ajdovščina</option>
                                                        <option value="Beltinci"
                                                            <?= $plot['admin_unit'] == 'Beltinci' ? 'selected' : '' ?>>
                                                            Beltinci</option>
                                                        <option value="Brežice"
                                                            <?= $plot['admin_unit'] == 'Brežice' ? 'selected' : '' ?>>
                                                            Brežice</option>
                                                        <option value="Celje"
                                                            <?= $plot['admin_unit'] == 'Celje' ? 'selected' : '' ?>>
                                                            Celje</option>
                                                        <option value="Cerknica"
                                                            <?= $plot['admin_unit'] == 'Cerknica' ? 'selected' : '' ?>>
                                                            Cerknica</option>
                                                        <option value="Črnomelj"
                                                            <?= $plot['admin_unit'] == 'Črnomelj' ? 'selected' : '' ?>>
                                                            Črnomelj</option>
                                                        <option value="Domžale"
                                                            <?= $plot['admin_unit'] == 'Domžale' ? 'selected' : '' ?>>
                                                            Domžale</option>
                                                        <option value="Dravograd"
                                                            <?= $plot['admin_unit'] == 'Dravograd' ? 'selected' : '' ?>>
                                                            Dravograd</option>
                                                        <option value="Gornja Radgona"
                                                            <?= $plot['admin_unit'] == 'Gornja Radgona' ? 'selected' : '' ?>>
                                                            Gornja Radgona</option>
                                                        <option value="Grosuplje"
                                                            <?= $plot['admin_unit'] == 'Grosuplje' ? 'selected' : '' ?>>
                                                            Grosuplje</option>
                                                        <option value="Hrastnik"
                                                            <?= $plot['admin_unit'] == 'Hrastnik' ? 'selected' : '' ?>>
                                                            Hrastnik</option>
                                                        <option value="Idrija"
                                                            <?= $plot['admin_unit'] == 'Idrija' ? 'selected' : '' ?>>
                                                            Idrija</option>
                                                        <option value="Ilirska Bistrica"
                                                            <?= $plot['admin_unit'] == 'Ilirska Bistrica' ? 'selected' : '' ?>>
                                                            Ilirska Bistrica</option>
                                                        <option value="Izola"
                                                            <?= $plot['admin_unit'] == 'Izola' ? 'selected' : '' ?>>
                                                            Izola</option>
                                                        <option value="Jesenice"
                                                            <?= $plot['admin_unit'] == 'Jesenice' ? 'selected' : '' ?>>
                                                            Jesenice</option>
                                                        <option value="Kamnik"
                                                            <?= $plot['admin_unit'] == 'Kamnik' ? 'selected' : '' ?>>
                                                            Kamnik</option>
                                                        <option value="Kočevje"
                                                            <?= $plot['admin_unit'] == 'Kočevje' ? 'selected' : '' ?>>
                                                            Kočevje</option>
                                                        <option value="Koper"
                                                            <?= $plot['admin_unit'] == 'Koper' ? 'selected' : '' ?>>
                                                            Koper</option>
                                                        <option value="Kranj"
                                                            <?= $plot['admin_unit'] == 'Kranj' ? 'selected' : '' ?>>
                                                            Kranj</option>
                                                        <option value="Krško"
                                                            <?= $plot['admin_unit'] == 'Krško' ? 'selected' : '' ?>>
                                                            Krško</option>
                                                        <option value="Laško"
                                                            <?= $plot['admin_unit'] == 'Laško' ? 'selected' : '' ?>>
                                                            Laško</option>
                                                        <option value="Lenart"
                                                            <?= $plot['admin_unit'] == 'Lenart' ? 'selected' : '' ?>>
                                                            Lenart</option>
                                                        <option value="Litija"
                                                            <?= $plot['admin_unit'] == 'Litija' ? 'selected' : '' ?>>
                                                            Litija</option>
                                                        <option value="Ljubljana"
                                                            <?= $plot['admin_unit'] == 'Ljubljana' ? 'selected' : '' ?>>
                                                            Ljubljana</option>
                                                        <option value="Ljutomer"
                                                            <?= $plot['admin_unit'] == 'Ljutomer' ? 'selected' : '' ?>>
                                                            Ljutomer</option>
                                                        <option value="Logatec"
                                                            <?= $plot['admin_unit'] == 'Logatec' ? 'selected' : '' ?>>
                                                            Logatec</option>
                                                        <option value="Maribor"
                                                            <?= $plot['admin_unit'] == 'Maribor' ? 'selected' : '' ?>>
                                                            Maribor</option>
                                                        <option value="Metlika"
                                                            <?= $plot['admin_unit'] == 'Metlika' ? 'selected' : '' ?>>
                                                            Metlika</option>
                                                        <option value="Mozirje"
                                                            <?= $plot['admin_unit'] == 'Mozirje' ? 'selected' : '' ?>>
                                                            Mozirje</option>
                                                        <option value="Murska Sobota"
                                                            <?= $plot['admin_unit'] == 'Murska Sobota' ? 'selected' : '' ?>>
                                                            Murska Sobota</option>
                                                        <option value="Nova Gorica"
                                                            <?= $plot['admin_unit'] == 'Nova Gorica' ? 'selected' : '' ?>>
                                                            Nova Gorica</option>
                                                        <option value="Novo mesto"
                                                            <?= $plot['admin_unit'] == 'Novo mesto' ? 'selected' : '' ?>>
                                                            Novo mesto</option>
                                                        <option value="Ormož"
                                                            <?= $plot['admin_unit'] == 'Ormož' ? 'selected' : '' ?>>
                                                            Ormož</option>
                                                        <option value="Pesnica"
                                                            <?= $plot['admin_unit'] == 'Pesnica' ? 'selected' : '' ?>>
                                                            Pesnica</option>
                                                        <option value="Piran"
                                                            <?= $plot['admin_unit'] == 'Piran' ? 'selected' : '' ?>>
                                                            Piran</option>
                                                        <option value="Postojna"
                                                            <?= $plot['admin_unit'] == 'Postojna' ? 'selected' : '' ?>>
                                                            Postojna</option>
                                                        <option value="Ptuj"
                                                            <?= $plot['admin_unit'] == 'Ptuj' ? 'selected' : '' ?>>Ptuj
                                                        </option>
                                                        <option value="Radlje ob Dravi"
                                                            <?= $plot['admin_unit'] == 'Radlje ob Dravi' ? 'selected' : '' ?>>
                                                            Radlje ob Dravi</option>
                                                        <option value="Radovljica"
                                                            <?= $plot['admin_unit'] == 'Radovljica' ? 'selected' : '' ?>>
                                                            Radovljica</option>
                                                        <option value="Ravne na Koroškem"
                                                            <?= $plot['admin_unit'] == 'Ravne na Koroškem' ? 'selected' : '' ?>>
                                                            Ravne na Koroškem</option>
                                                        <option value="Ribnica"
                                                            <?= $plot['admin_unit'] == 'Ribnica' ? 'selected' : '' ?>>
                                                            Ribnica</option>
                                                        <option value="Ruše"
                                                            <?= $plot['admin_unit'] == 'Ruše' ? 'selected' : '' ?>>Ruše
                                                        </option>
                                                        <option value="Sevnica"
                                                            <?= $plot['admin_unit'] == 'Sevnica' ? 'selected' : '' ?>>
                                                            Sevnica</option>
                                                        <option value="Sežana"
                                                            <?= $plot['admin_unit'] == 'Sežana' ? 'selected' : '' ?>>
                                                            Sežana</option>
                                                        <option value="Slovenj Gradec"
                                                            <?= $plot['admin_unit'] == 'Slovenj Gradec' ? 'selected' : '' ?>>
                                                            Slovenj Gradec</option>
                                                        <option value="Slovenska Bistrica"
                                                            <?= $plot['admin_unit'] == 'Slovenska Bistrica' ? 'selected' : '' ?>>
                                                            Slovenska Bistrica</option>
                                                        <option value="Slovenske Konjice"
                                                            <?= $plot['admin_unit'] == 'Slovenske Konjice' ? 'selected' : '' ?>>
                                                            Slovenske Konjice</option>
                                                        <option value="Šentjur"
                                                            <?= $plot['admin_unit'] == 'Šentjur' ? 'selected' : '' ?>>
                                                            Šentjur</option>
                                                        <option value="Škofja Loka"
                                                            <?= $plot['admin_unit'] == 'Škofja Loka' ? 'selected' : '' ?>>
                                                            Škofja Loka</option>
                                                        <option value="Tolmin"
                                                            <?= $plot['admin_unit'] == 'Tolmin' ? 'selected' : '' ?>>
                                                            Tolmin</option>
                                                        <option value="Trbovlje"
                                                            <?= $plot['admin_unit'] == 'Trbovlje' ? 'selected' : '' ?>>
                                                            Trbovlje</option>
                                                        <option value="Trebnje"
                                                            <?= $plot['admin_unit'] == 'Trebnje' ? 'selected' : '' ?>>
                                                            Trebnje</option>
                                                        <option value="Tržič"
                                                            <?= $plot['admin_unit'] == 'Tržič' ? 'selected' : '' ?>>
                                                            Tržič</option>
                                                        <option value="Velenje"
                                                            <?= $plot['admin_unit'] == 'Velenje' ? 'selected' : '' ?>>
                                                            Velenje</option>
                                                        <option value="Vrhnika"
                                                            <?= $plot['admin_unit'] == 'Vrhnika' ? 'selected' : '' ?>>
                                                            Vrhnika</option>
                                                        <option value="Zagorje ob Savi"
                                                            <?= $plot['admin_unit'] == 'Zagorje ob Savi' ? 'selected' : '' ?>>
                                                            Zagorje ob Savi</option>
                                                        <option value="Zalec"
                                                            <?= $plot['admin_unit'] == 'Zalec' ? 'selected' : '' ?>>
                                                            Zalec</option>
                                                        <option value="Ziri"
                                                            <?= $plot['admin_unit'] == 'Ziri' ? 'selected' : '' ?>>Ziri
                                                        </option>
                                                    </select>

                                                </div>
                                                <div class="col">
                                                    <label class="form-label">Cadastral Number <sup class='text-danger'>*</sup></label>
                                                    <select name="cadastral_number[]"
                                                        class="form-control cadastral_number" id="cadastral_number"
                                                        required>
                                                       
                                                        <?php
                                                        foreach($cadastral_numbers as $cadastral_number){
                                                        ?>
                                                        <option value="<?=$cadastral_number['cadastral_number']?>"
                                                            <?= $plot['cadastral_number'] == $cadastral_number['cadastral_number'] ? 'selected' : '' ?>>
                                                            <?=$cadastral_number['name']?></option>
                                                        <?php
                                                        }
                                                        ?>

                                                    </select>

                                                </div>
                                                <div class="col">
                                                    <label class="form-label">Plot Number</label>
                                                    <input type="text" class="form-control" name="plot_number[]"
                                                        placeholder="Plot Number"
                                                        value="<?= htmlspecialchars($plot['plot_number']) ?>">
                                                </div>
                                                <div class="col">
                                                    <label class="form-label">Plot Type <sup class='text-danger'>*</sup></label>
                                                    <select id="plotType" name="plot_type[]" class="form-control"
                                                        required>
                                                        
                                                        <option value="meadow"
                                                            <?= $plot['plot_type'] == 'meadow' ? 'selected' : '' ?>>
                                                            Meadow</option>
                                                        <option value="field"
                                                            <?= $plot['plot_type'] == 'field' ? 'selected' : '' ?>>Field
                                                        </option>
                                                        <option value="forest"
                                                            <?= $plot['plot_type'] == 'forest' ? 'selected' : '' ?>>
                                                            Forest</option>

                                                        <option
                                                            <?= $plot['plot_type'] == 'building land' ? 'selected' : '' ?>
                                                            value="building land">Building Land</option>
                                                        <option
                                                            <?= $plot['plot_type'] == 'building on land' ? 'selected' : '' ?>
                                                            value="building on land">Building On Land</option>
                                                        <option <?= $plot['plot_type'] == 'other' ? 'selected' : '' ?>
                                                            value="other">Other</option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="form-row mb-3">
                                                <div class="col">
                                                    <label class="form-label">Building Exist</label>
                                                    <input type="checkbox" class="d-block mt-3" name="building_exists[]"
                                                        value="1" <?= $plot['building_exists'] ? 'checked' : '' ?>>
                                                </div>
                                                <div class="col">
                                                    <label class="form-label">Latitude <sup class='text-danger'>*</sup></label>
                                                    <input type="text" class="form-control" name="latitude[]"
                                                        placeholder="Latitude"
                                                        value="<?= htmlspecialchars($plot['latitude']) ?>" required>
                                                </div>
                                                <div class="col">
                                                    <label class="form-label">Longitude <sup class='text-danger'>*</sup></label>
                                                    <input type="text" class="form-control" name="longitude[]"
                                                        placeholder="Longitude"
                                                        value="<?= htmlspecialchars($plot['longitude']) ?>" required>
                                                </div>
                                                <div class="col">
                                                    <label class="form-label">Address</label>
                                                    <input type="text" class="form-control" name="address[]"
                                                        placeholder="Address"
                                                        value="<?= htmlspecialchars($plot['address']) ?>" required>
                                                </div>
                                            </div>
                                            <div class="form-row mb-3">
                                                <div class="col">
                                                    <label class="form-label">Land Leased</label>
                                                    <input type="checkbox" class="d-block mt-3" name="land_leased[]"
                                                        value="1" <?= $plot['land_leased'] ? 'checked' : '' ?>>
                                                </div>
                                                <div class="col">
                                                    <label class="form-label">Land Buildable</label>
                                                    <input type="checkbox" class="d-block mt-3" name="land_buildable[]"
                                                        value="1" <?= $plot['land_buildable'] ? 'checked' : '' ?>>
                                                </div>
                                                <div class="col">
                                                    <label class="form-label">Price per sqm</label>
                                                    <input type="number" class="form-control" name="price_per_sqm[]"
                                                        placeholder="Price per sqm"
                                                        value="<?= htmlspecialchars($plot['price_per_sqm']) ?>">
                                                </div>
                                                <div class="col">
                                                    <label class="form-label">Square Meters</label>
                                                    <input type="number" class="form-control" name="square_meters[]"
                                                        placeholder="Square Meters"
                                                        value="<?= htmlspecialchars($plot['square_meters']) ?>">
                                                </div>
                                            </div>
                                            <div class="form-row mb-3">
                                                <div class="col">
                                                    <label class="form-label">Final Price</label>
                                                    <input type="number" class="form-control" name="final_price[]"
                                                        placeholder="Final Price"
                                                        value="<?= htmlspecialchars($plot['final_price']) ?>">
                                                </div>
                                                <div class="col">
                                                    <label class="form-label">Start Date <sup class='text-danger'>*</sup></label>
                                                    <input type="date" class="form-control" name="start_date[]"
                                                        value="<?= htmlspecialchars($plot['start_date']) ?>" required>
                                                </div>
                                                <div class="col">
                                                    <label class="form-label">Expiration Date</label>
                                                    <input type="date" class="form-control" name="expiration_date[]"
                                                        value="<?= htmlspecialchars($plot['expiration_date']) ?>">
                                                </div>
                                                
                                            </div>

                                            <div class="form-row mb-3">


                                                <div class="col-6">
                                                    <label class="form-label">Auction Date</label>
                                                    <input type="date" value="<?= htmlspecialchars($plot['auction_date']) ?>" class="form-control" name="auction_date[]">
                                                </div>
                                                <div class="col-3">
                                                    <label class="form-label">Share in Real Estate(%)</label>
                                                    <input type="number" value="<?= htmlspecialchars($plot['share']) ?>" class="form-control" name="share[]">
                                                </div>

                                                <div class="col-3 mt-4">
                                                    <?php if ($index == 0): ?>
                                                    <button type="button" class="btn btn-success add-variant">+</button>
                                                    <?php else: ?>
                                                    <button type="button"
                                                        class="btn btn-danger remove-variant">-</button>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </div>
                                        <?php $index++; endforeach; ?>
                                    </div>
                                </div>
                            </div>







                            <div class="form-group row">
                                <label class="col-sm-2 col-form-label">Photos</label>
                                <div class="col-sm-10">
                                    <input type="file" class="form-control" name="photos[]" multiple>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label class="col-sm-2 col-form-label">Description <sup class='text-danger'>*</sup></label>
                                <div class="col-sm-10">
                                    <textarea class="form-control" name="description"
                                        required><?= htmlspecialchars($property['description']); ?></textarea>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label class="col-sm-2 col-form-label">Document Link</label>
                                <div class="col-sm-10">
                                    <input type="text" class="form-control" name="document_link"
                                        value="<?= htmlspecialchars($property['document_link']); ?>">
                                </div>
                            </div>

                            <div class="form-group row">
                                <label class="col-sm-2 col-form-label">Rent or Sell <sup class='text-danger'>*</sup></label>
                                <div class="col-sm-10">
                                    <select class="form-control" name="rent_or_sell" required>
                                        <option value="rent"
                                            <?= $property['rent_or_sell'] === 'rent' ? 'selected' : ''; ?>>Rent</option>
                                        <option value="sell"
                                            <?= $property['rent_or_sell'] === 'sell' ? 'selected' : ''; ?>>Sell</option>
                                    </select>
                                </div>
                            </div>



                            <div class="form-group mb-0">
                                <div class='text-center'>
                                    <button type="submit" class="btn btn-primary waves-effect waves-light">
                                        Save
                                    </button>
                                    <button type="reset" class="btn btn-secondary waves-effect m-l-5">
                                        Reset
                                    </button>
                                </div>
                            </div>
                        </form>


                    </div>
                </div>
            </div>
            <!-- end col -->
        </div>
        <!-- end row -->

    </div>
    <!-- container-fluid -->

</div>
<!-- content -->

<script>
$(document).ready(function() {
    $('.admin_unit').select2({
        minimumResultsForSearch: 10 // at least 20 results must be displayed
    });

    $('.cadastral_number').select2({
        minimumResultsForSearch: 10 // at least 20 results must be displayed
    });

});
</script>

<script>
$(document).on('click', '.add-variant', function() {

    let variant_group = `
    <div class="variant-group border p-3 rounded mb-3">
                                            <div class="form-row mb-3">
                                                <div class="col">
                                                    <label class="form-label">Administrative Unit</label>
                                                    <select name="admin_unit[]" class="form-control admin_unit"
                                                        required>
                                                        <option value="" disabled selected>Select Administrative Unit
                                                        </option>
                                                        <option value="Ajdovščina">Ajdovščina</option>
                                                        <option value="Beltinci">Beltinci</option>
                                                        <option value="Brežice">Brežice</option>
                                                        <option value="Celje">Celje</option>
                                                        <option value="Cerknica">Cerknica</option>
                                                        <option value="Črnomelj">Črnomelj</option>
                                                        <option value="Domžale">Domžale</option>
                                                        <option value="Dravograd">Dravograd</option>
                                                        <option value="Gornja Radgona">Gornja Radgona</option>
                                                        <option value="Grosuplje">Grosuplje</option>
                                                        <option value="Hrastnik">Hrastnik</option>
                                                        <option value="Idrija">Idrija</option>
                                                        <option value="Ilirska Bistrica">Ilirska Bistrica</option>
                                                        <option value="Izola">Izola</option>
                                                        <option value="Jesenice">Jesenice</option>
                                                        <option value="Kamnik">Kamnik</option>
                                                        <option value="Kočevje">Kočevje</option>
                                                        <option value="Koper">Koper</option>
                                                        <option value="Kranj">Kranj</option>
                                                        <option value="Krško">Krško</option>
                                                        <option value="Laško">Laško</option>
                                                        <option value="Lenart">Lenart</option>
                                                        <option value="Litija">Litija</option>
                                                        <option value="Ljubljana">Ljubljana</option>
                                                        <option value="Ljutomer">Ljutomer</option>
                                                        <option value="Logatec">Logatec</option>
                                                        <option value="Maribor">Maribor</option>
                                                        <option value="Metlika">Metlika</option>
                                                        <option value="Mozirje">Mozirje</option>
                                                        <option value="Murska Sobota">Murska Sobota</option>
                                                        <option value="Nova Gorica">Nova Gorica</option>
                                                        <option value="Novo mesto">Novo mesto</option>
                                                        <option value="Ormož">Ormož</option>
                                                        <option value="Pesnica">Pesnica</option>
                                                        <option value="Piran">Piran</option>
                                                        <option value="Postojna">Postojna</option>
                                                        <option value="Ptuj">Ptuj</option>
                                                        <option value="Radlje ob Dravi">Radlje ob Dravi</option>
                                                        <option value="Radovljica">Radovljica</option>
                                                        <option value="Ravne na Koroškem">Ravne na Koroškem</option>
                                                        <option value="Ribnica">Ribnica</option>
                                                        <option value="Ruše">Ruše</option>
                                                        <option value="Sevnica">Sevnica</option>
                                                        <option value="Sežana">Sežana</option>
                                                        <option value="Slovenj Gradec">Slovenj Gradec</option>
                                                        <option value="Slovenska Bistrica">Slovenska Bistrica</option>
                                                        <option value="Slovenske Konjice">Slovenske Konjice</option>
                                                        <option value="Šentjur">Šentjur</option>
                                                        <option value="Škofja Loka">Škofja Loka</option>
                                                        <option value="Tolmin">Tolmin</option>
                                                        <option value="Trbovlje">Trbovlje</option>
                                                        <option value="Trebnje">Trebnje</option>
                                                        <option value="Tržič">Tržič</option>
                                                        <option value="Velenje">Velenje</option>
                                                        <option value="Vrhnika">Vrhnika</option>
                                                        <option value="Zagorje ob Savi">Zagorje ob Savi</option>
                                                        <option value="Zalec">Zalec</option>
                                                        <option value="Ziri">Ziri</option>
                                                    </select>
                                                </div>
                                                <div class="col">
                                                    <label class="form-label">Cadastral Number</label>
                                                    <select name='cadastral_number[]'
                                                        class="form-control cadastral_number" required>
                                                        <option value="">Select Cadastral Number</option>
                                                        <?php
                                                        foreach($cadastral_numbers as $cadastral_number){
                                                        ?>
                                                        <option value="<?=$cadastral_number['cadastral_number']?>">
                                                            <?=$cadastral_number['name']?></option>
                                                        <?php
                                                        }
                                                        ?>
                                                    </select>
                                                </div>
                                                <div class="col">
                                                    <label class="form-label">Plot Number</label>
                                                    <input type="text" class="form-control" name="plot_number[]"
                                                        placeholder="Plot Number">
                                                </div>
                                                <div class="col">
                                                    <label class="form-label">Plot Type</label>
                                                    <select id="plotType" name="plot_type" class="form-control"
                                                        required>
                                                        <option value="" disabled selected>Select Plot Type</option>
                                                        <option value="meadow">Meadow</option>
                                                        <option value="field">Field</option>
                                                        <option value="forest">Forest</option>
                                                        <option value="building land">Building Land</option>
                                                        <option value="building on land">Building On Land</option>
                                                        <option value="other">Other</option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="form-row mb-3">
                                                <div class="col">
                                                    <label class="form-label">Building Exist</label>
                                                    <input type="checkbox" class="d-block mt-3" name="building_exists[]"
                                                        value="1">

                                                </div>
                                                <div class="col">
                                                    <label class="form-label">Latitude <button
                                                            class='badge bg-success getLocationBtn'>Get Current
                                                            Locations</button></label>
                                                    <input type="text" class="form-control latitude" name="latitude[]"
                                                        placeholder="Latitude" required>
                                                </div>
                                                <div class="col">
                                                    <label class="form-label">Longitude</label>
                                                    <input type="text" class="form-control longitude" name="longitude[]"
                                                        placeholder="Longitude" required>

                                                </div>
                                                <div class="col">
                                                    <label class="form-label">Address</label>
                                                    <input type="text" class="form-control" name="address[]"
                                                        placeholder="Address" required>

                                                </div>
                                            </div>

                                            <div class="form-row mb-3">
                                                <div class="col">
                                                    <label class="form-label">Land Leased</label>
                                                    <input class="d-block mt-3" type="checkbox" name="land_leased[]"
                                                        value="1">

                                                </div>
                                                <div class="col">
                                                    <label class="form-label">Land Buildable</label>

                                                    <input class="d-block mt-3" type="checkbox" name="land_buildable[]"
                                                        value="1">


                                                </div>
                                                <div class="col">
                                                    <label class="form-label">Price per sqm</label>

                                                    <input type="number" class="form-control" name="price_per_sqm[]"
                                                        placeholder="Price per sqm">

                                                </div>

                                                <div class="col">

                                                    <label class="form-label">Square Meters</label>

                                                    <input type="number" class="form-control" name="square_meters"
                                                        placeholder="Square Meters">


                                                </div>

                                            </div>

                                            <div class="form-row mb-3">
                                                <div class="col">
                                                    <label class="form-label">Package Sale</label>
                                                    <input type="checkbox" class="d-block mt-2" name="package_sale[]"
                                                        value="1">

                                                </div>



                                                <div class="col">
                                                    <label class="form-label">Final Price</label>
                                                    <input type="number" class="form-control" name="final_price[]"
                                                        placeholder="Final Price">
                                                </div>

                                                <div class="col">
                                                    <label class="form-label">Start Date</label>
                                                    <input type="date" class="form-control" name="start_date[]">
                                                </div>

                                                <div class="col">
                                                    <label class="form-label">Expiration Date</label>
                                                    <input type="date" class="form-control" name="expiration_date[]">
                                                </div>

                                            </div>

                                            <div class="form-row mb-3">


                                                <div class="col-6">
                                                    <label class="form-label">Auction Date</label>
                                                    <input type="date" class="form-control" name="auction_date[]">
                                                </div>

                                                <div class="col-3">
                                                    <label class="form-label">Share in Real Estate(%)</label>
                                                    <input type="number" class="form-control" name="share[]">
                                                </div>

                                                <div class="col-3 mt-4 text-right">
                                                    <button type="button" class="btn btn-danger remove-variant">Remove Property</button>
                                                </div>
                                            </div>

                                        </div>
    
    `;
    var variantContainer = $('#variant-container');
        variantContainer.append(variant_group);
        variantContainer.find('.admin_unit, .cadastral_number').select2({
    });
});

$(document).on('click', '.remove-variant', function() {
    $(this).closest('.variant-group').remove();
});

</script>
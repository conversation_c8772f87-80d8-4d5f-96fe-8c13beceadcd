 <!-- Start content -->
 <div class="content">

     <div class="container-fluid">
         <div class="page-title-box">

             <div class="row align-items-center ">
                 <div class="col-md-8">
                     <div class="page-title-box">
                         <h4 class="page-title">Subscription's Record</h4>

                     </div>
                 </div>




             </div>
         </div>
         <!-- end page-title -->

         <div class="row">
             <div class="col-12">
                 <div class="card">
                     <div class="card-body">
                         <div class="table-responsive">

                             <?php if(count($list) > 0) { ?>


                             <table id="datatable-buttons"
                                 class="table table-bordered table-striped dt-responsive nowrap"
                                 style="border-collapse: collapse; border-spacing: 0; width: 100%;">
                                 <thead>
                                     <tr>
                                         <th>Id</th>
                                         <th>Customer Name</th>
                                         <th>Email Address</th>
                                         <th>Phone Number</th>
                                         <th>Package Name</th>
                                         <th>Payment Method</th>
                                         <th>Price</th>
                                         <th>Start Date</th>
                                         <th>End Date</th>
                                         <th>Status</th>
                                         <th>Action</th>

                                     </tr>

                                 </thead>

                                 <tbody>
                                     <?php $i=1; foreach ($list as $subscription) {
                                        $payment_method = str_replace('_', ' ', $subscription->payment_method);
                                        $payment_method = ucwords($payment_method); 
                                        $start_date = $subscription->start_date;
                                        $start_date = new DateTime($start_date);
                                        $_start_date = $start_date->format('d F Y');
                                        $end_date = $subscription->end_date;
                                        $end_date = new DateTime($end_date);
                                        $_end_date = $end_date->format('d F Y');
                                    ?>
                                     <tr>
                                         <td><?=$i?></td>
                                         <td><?=$subscription->first_name." ".$subscription->last_name?></td>
                                         <td><?=$subscription->email?></td>
                                         <td><?=$subscription->phone_number?></td>
                                         <td><?=$subscription->title?></td>
                                         <td><?=$payment_method?></td>

                                         <td><?=$subscription->paidAmount?></td>
                                         <td><?=$_start_date?></td>
                                         <td><?=$_end_date?></td>
                                         <td>
                                             <?php
                                         if($subscription->status == "active"){
                                            ?>
                                             <span class='badge badge-success'>Active</span>
                                             <?php
                                         }
                                         elseif ($subscription->status == "pending") {
                                            ?>
                                             <span class='badge badge-warning'>Pending</span>
                                             <?php
                                         }
                                         else{
                                            ?>
                                             <span class='badge badge-danger'>Expired</span>
                                             <?php
                                         }
                                         ?>
                                         </td>
                                         <td>
                                             <div class="btn-group">
                                                 <button class="btn btn-sm btn-primary dropdown-toggle" type="button"
                                                     data-toggle="dropdown" aria-haspopup="true"
                                                     aria-expanded="false">Action</button>
                                                 <div class="dropdown-menu">
                                                     <?php
if ($subscription->status == "pending") {
?>
                                                     <a  class="dropdown-item activate"
                                                         data-id='<?=$subscription->subId?>' href="#">Activate</a>
                                                     <?php
}
?>



                                                 </div>
                                             </div>
                                         </td>

                                     </tr>
                                     <?php
                            $i++;}
                            ?>
                                 </tbody>
                             </table>
                         </div>
                         <?php
                    }else{
                    ?>
                         <div class="alert alert-danger wow fadeInUp" role="alert"> No Data Found! </div>
                         <?php
                    }
                    ?>
                     </div>
                 </div>
             </div>
             <!-- end col -->
         </div>
         <!-- end row -->



     </div>
     <!-- container-fluid -->

 </div>
 <!-- content -->

 <script>
$(document).on('click', '.activate', function(e) {
    e.preventDefault(); // Prevent the default action (if any)

    var subscriptionId = $(this).data('id'); // Get the subscription ID from the data-id attribute

    // SweetAlert to confirm the action
    Swal.fire({
        title: 'Are you sure?',
        text: "Do you want to activate this user?",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: 'Yes, activate it!',
        cancelButtonText: 'No, cancel!',
        reverseButtons: true
    }).then((result) => {
        if (result.isConfirmed) {
            // Send AJAX request to update the subscription status
            $.ajax({
                url: '<?=base_url()?>admin/subscriptions/activateSubscription', // Replace with your actual endpoint
                type: 'POST',
                data: {
                    subscription_id: subscriptionId,
                    status: 'active' // The new status you want to set
                },
                success: function(response) {
                    let data = JSON.parse(response);
                    if (data.success) {
                        // Show success message if the status was updated
                        Swal.fire(
                            'Activated!',
                            'User subscription has been activated.',
                            'success'
                        );
                        location.reload();
                    } else {
                        // Handle any errors that might occur during the AJAX request
                        Swal.fire(
                            'Error!',
                            'There was an issue activating the user.',
                            'error'
                        );
                    }
                },
                error: function() {
                    // Show error message if the AJAX request fails
                    Swal.fire(
                        'Error!',
                        'An error occurred while processing your request.',
                        'error'
                    );
                }
            });
        }
    });
});
 </script>
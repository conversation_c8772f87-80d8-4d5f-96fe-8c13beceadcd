 <!-- Start content -->
 <div class="content">

     <div class="container-fluid">
         <div class="page-title-box">

             <div class="row align-items-center ">
                 <div class="col-md-8">
                     <div class="page-title-box">
                         <h4 class="page-title">User's Record</h4>

                     </div>
                 </div>
                 



             </div>
         </div>
         <!-- end page-title -->

         <div class="row">
             <div class="col-12">
                 <div class="card">
                     <div class="card-body">
                         <div class="table-responsive">

                             <?php if(count($list) > 0) { ?>


                             <table id="datatable-buttons"
                                 class="table table-bordered table-striped dt-responsive nowrap"
                                 style="border-collapse: collapse; border-spacing: 0; width: 100%;">
                                 <thead>
                                     <tr>
                                         <th>Id</th>
                                         <th>First Name</th>
                                         <th>Last Name</th>
                                         <th>Email Address</th>
                                         <th>Phone Number</th>
                                         <th>Status</th>
                                         <th>Action</th>
                                         
                                     </tr>

                                 </thead>

                                 <tbody>
                                     <?php $i=1; foreach ($list as $user) { ?>
                                     <tr>
                                         <td><?=$i?></td>
                                         <td><?=$user->first_name?></td>
                                         <td><?=$user->last_name?></td>
                                         
                                         <td><?=$user->email?></td>
                                         <td><?=$user->phone_number?></td>
                                         <td>
                                             <?php
                                         if($user->is_verified == 1){
                                            ?>
                                         <span class='badge badge-success'>Active</span>
                                             <?php
                                         }
                                         else{
                                            ?>
                                             <span class='badge badge-danger'>Deactive</span>
                                             <?php
                                         }
                                         ?>
                                         </td>
                                         <td>
                                             <div class="btn-group">
                                                 <button class="btn btn-sm btn-primary dropdown-toggle" type="button"
                                                     data-toggle="dropdown" aria-haspopup="true"
                                                     aria-expanded="false">Action</button>
                                                 <div class="dropdown-menu">

                                                         <a class="dropdown-item"
                                                         href="<?= site_url() ?>master/users/subscriptions/<?= $user->id?>">Subscriptions</a>



                                                 </div>
                                             </div>
                                         </td>
                                        
                                     </tr>
                                     <?php
                            $i++;}
                            ?>
                                 </tbody>
                             </table>
                         </div>
                         <?php
                    }else{
                    ?>
                         <div class="alert alert-danger wow fadeInUp" role="alert"> No Data Found! </div>
                         <?php
                    }
                    ?>
                     </div>
                 </div>
             </div>
             <!-- end col -->
         </div>
         <!-- end row -->



     </div>
     <!-- container-fluid -->

 </div>
 <!-- content -->
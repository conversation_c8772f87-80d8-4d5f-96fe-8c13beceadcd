 <!-- Start content -->
 <div class="content">

     <div class="container-fluid">
         <div class="page-title-box">

             <div class="row align-items-center ">
                 <div class="col-md-8">
                     <div class="page-title-box">
                         <h4 class="page-title">Subscription's Record</h4>

                     </div>
                 </div>




             </div>
         </div>
         <!-- end page-title -->

         <div class="row">
             <div class="col-12">
                 <div class="card">
                     <div class="card-body">
                         <div class="table-responsive">

                             <?php if(count($list) > 0) { ?>


                             <table id="datatable-buttons"
                                 class="table table-bordered table-striped dt-responsive nowrap"
                                 style="border-collapse: collapse; border-spacing: 0; width: 100%;">
                                 <thead>
                                     <tr>
                                         <th>Id</th>
                                         <th>Package Name</th>
                                         <th>Payment Method</th>
                                         <th>Price</th>
                                         <th>Start Date</th>
                                         <th>End Date</th>
                                         <th>Status</th>

                                     </tr>

                                 </thead>

                                 <tbody>
                                     <?php $i=1; foreach ($list as $subscription) {
                                        $payment_method = str_replace('_', ' ', $subscription->payment_method);
                                        $payment_method = ucwords($payment_method); 
                                        $start_date = $subscription->start_date;
                                        $start_date = new DateTime($start_date);
                                        $_start_date = $start_date->format('d F Y');
                                        $end_date = $subscription->end_date;
                                        $end_date = new DateTime($end_date);
                                        $_end_date = $end_date->format('d F Y');
                                    ?>
                                     <tr>
                                         <td><?=$i?></td>
                                         <td><?=$subscription->title?></td>
                                         <td><?=$payment_method?></td>

                                         <td><?=$subscription->paidAmount?></td>
                                         <td><?=$_start_date?></td>
                                         <td><?=$_end_date?></td>
                                         <td>
                                             <?php
                                         if($subscription->status == "active"){
                                            ?>
                                             <span class='badge badge-success'>Active</span>
                                             <?php
                                         }
                                         elseif ($subscription->status == "pending") {
                                            ?>
                                             <span class='badge badge-warning'>Pending</span>
                                             <?php
                                         }
                                         else{
                                            ?>
                                             <span class='badge badge-danger'>Expired</span>
                                             <?php
                                         }
                                         ?>
                                         </td>


                                     </tr>
                                     <?php
                            $i++;}
                            ?>
                                 </tbody>
                             </table>
                         </div>
                         <?php
                    }else{
                    ?>
                         <div class="alert alert-danger wow fadeInUp" role="alert"> No Data Found! </div>
                         <?php
                    }
                    ?>
                     </div>
                 </div>
             </div>
             <!-- end col -->
         </div>
         <!-- end row -->



     </div>
     <!-- container-fluid -->

 </div>
 <!-- content -->
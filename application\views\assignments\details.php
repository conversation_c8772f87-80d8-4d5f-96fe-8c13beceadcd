<main class="main">

    <!-- Page Title -->
    <div class="page-title" data-aos="fade">
        <div class="heading">
            <div class="container">
                <div class="row d-flex justify-content-center text-center">
                    <div class="col-lg-8">
                        <h1>Assignment Details</h1>
                    </div>
                </div>
            </div>
        </div>
        <nav class="breadcrumbs">
            <div class="container">
                <ol>
                    <li><a href="<?=base_url()?>">Home</a></li>
                    <li class="current">Assignment Details</li>
                </ol>
            </div>
        </nav>
    </div><!-- End Page Title -->

    <!-- Courses Course Details Section -->
    <section id="courses-course-details" class="courses-course-details section">

        <div class="container" data-aos="fade-up">

            <div class="row">
                <div class="col-lg-8">
                    <img src="<?= base_url('assets/img/assignments/banners/' . $assignment->banner); ?>"
                        class="img-fluid" alt="">
                    <h3><?=$assignment->title?></h3>
                    <p>
                        <?=$assignment->description?>
                    </p>
                </div>
                <div class="col-lg-4">

                    <div class="course-info d-flex justify-content-between align-items-center">
                        <h5>Budget</h5>
                        <p>$<?=$assignment->budget?></p>
                    </div>

                     <?php
                    if($assignment->pricing_type && !empty($assignment->pricing_type)){
                    ?>
                    <div class="mb-2">
                        <strong>Payment Type:</strong>
                        <?php if ($assignment->pricing_type === 'hourly'): ?>
                        Hourly ($<?= $assignment->hourly_rate ?>)
                        <?php elseif ($assignment->pricing_type === 'hourly_bonus'): ?>
                        Hourly ($<?= $assignment->hourly_rate ?>) + Bonus
                        <br><strong>Bonus:</strong> <?= $assignment->bonus_details ?>
                        <?php elseif ($assignment->pricing_type === 'commission'): ?>
                        Commission-Based
                        <br><strong>Structure:</strong> <?= $assignment->commission_details ?>
                        <?php endif; ?>
                    </div>
                    <?php
                    }
                    ?>

                    <div class="course-info d-flex justify-content-between align-items-center">
                        <h5>Deadline</h5>
                        <p><?=$assignment->deadline?></p>
                    </div>

                   

                    <div class="course-info">
                        <?php
                            if($this->session->userdata('s_id')){
                            ?>
                        <a href="<?= base_url('assignments/place_bid/' . $assignment->id); ?>"
                            class="btn btn-success d-block w-100 text-white">
                            <i class='fa fa-book'></i> Take This Deal
                        </a>
                        <?php }else{?>
                        <a href="<?= base_url('login'); ?>" class="btn btn-success d-block w-100 text-white">
                            <i class='fa fa-book'></i> Take This Deal
                        </a>
                        <?php
                            }
                            ?>
                    </div>

                </div>
            </div>

        </div>

    </section>

</main>
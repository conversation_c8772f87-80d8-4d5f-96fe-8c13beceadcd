<main class="main">
    <!-- Page Title -->
    <div class="page-title" data-aos="fade">
        <div class="heading">
            <div class="container">
                <div class="row d-flex justify-content-center text-center">
                    <div class="col-lg-8">
                        <h1>Assignments<br></h1>

                    </div>
                </div>
            </div>
        </div>
        <nav class="breadcrumbs">
            <div class="container">
                <ol>
                    <li><a href="<?= base_url() ?>">Home</a></li>
                    <li class="current">Assignments<br></li>
                </ol>
            </div>
        </nav>
    </div><!-- End Page Title -->
    <section id="courses" class="courses section">
        <div class="container">

            <div class="search-bar-container mb-5">
                <form method='GET' action="<?=base_url()?>assignments" class='w-100'>
                    <input type="text" id="searchInput" name='search' class="search-bar w-75" placeholder="Search Here">
                    <button id="searchButton" class="btn search-button"><i class="fa fa-search"></i> Search</button>
                </form>
            </div>

            <?php
            if($this->session->flashdata('bid_error'))
            {
                echo '
                <div class="alert alert-danger">
                '.$this->session->flashdata("bid_error").'
                </div>
                ';
            }
        ?>
            <?php
            if($this->session->flashdata('bid_success'))
            {
                echo '
                <div class="alert alert-success">
                '.$this->session->flashdata("bid_success").'
                </div>
                ';
            }
        ?>
            <?php if (empty($assignments)): ?>
            <div class="alert alert-danger text-center" role="alert">
                No jobs available at the moment.
            </div>
            <?php else: ?>
            <div class="row">
                <?php foreach ($assignments as $assignment): ?>
                <?php if ($this->session->userdata('s_id') || $assignment->is_public == 1): ?>
                <div class="col-lg-4 col-md-6 d-flex align-items-stretch mt-4 mt-lg-0" data-aos="zoom-in"
                    data-aos-delay="300">
                    <div class="course-item">
                        <img src="<?= base_url('assets/img/assignments/banners/' . $assignment->banner); ?>"
                            class="img-fluid" alt="...">
                        <div class="course-content">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <p class="category"><?= $assignment->industry; ?></p>
                            </div>

                            <h3>
                                <a href="<?= base_url('assignments/details/' . $assignment->id); ?>">
                                    <?= $assignment->title ?>
                                </a>
                            </h3>
                            <p class="description">
                                <?= substr($assignment->description, 0, 250); ?>
                            </p>

                            <!-- Payment Structure Section -->
                             <?php
                    if($assignment->pricing_type && !empty($assignment->pricing_type)){
                    ?>
                            <div class="mb-2">
                                <strong>Payment Type:</strong>
                                <?php if ($assignment->pricing_type === 'hourly'): ?>
                                Hourly ($<?= $assignment->hourly_rate ?>)
                                <?php elseif ($assignment->pricing_type === 'hourly_bonus'): ?>
                                Hourly ($<?= $assignment->hourly_rate ?>) + Bonus
                                <br><strong>Bonus:</strong> <?= $assignment->bonus_details ?>
                                <?php elseif ($assignment->pricing_type === 'commission'): ?>
                                Commission-Based
                                <br><strong>Structure:</strong> <?= $assignment->commission_details ?>
                                <?php endif; ?>
                            </div>
                            <?php
                    }
                    ?>

                            <p class="card-text">
                                B: <?= $assignment->bid_count; ?>
                            </p>

                            <div class="trainer">
                                <?php if ($this->session->userdata('s_id')): ?>
                                <a href="<?= base_url('assignments/place_bid/' . $assignment->id); ?>"
                                    class="btn btn-success d-block w-100">
                                    <i class='fa fa-book'></i> Take This Deal
                                </a>
                                <?php else: ?>
                                <a href="<?= base_url('login'); ?>" class="btn btn-success d-block w-100">
                                    <i class='fa fa-book'></i> Take This Deal
                                </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>

                <?php endif; ?>
                <?php endforeach; ?>
            </div>
            <?php endif; ?>


            <div class="mt-4">
                <?= $pagination_links; ?>
            </div>
        </div>
    </section>
</main>


<!-- Scroll Top -->
<a href="#" id="scroll-top" class="scroll-top d-flex align-items-center justify-content-center"><i
        class="bi bi-arrow-up-short"></i></a>


<!-- Vendor JS Files -->
<script src="<?=base_url()?>assets/vendor/bootstrap/js/bootstrap.bundle.min.js"></script>
<script src="<?=base_url()?>assets/vendor/php-email-form/validate.js"></script>
<script src="<?=base_url()?>assets/vendor/aos/aos.js"></script>
<script src="<?=base_url()?>assets/vendor/glightbox/js/glightbox.min.js"></script>
<script src="<?=base_url()?>assets/vendor/purecounter/purecounter_vanilla.js"></script>
<script src="<?=base_url()?>assets/vendor/swiper/swiper-bundle.min.js"></script>

<!-- Main JS File -->
<script src="<?=base_url()?>assets/js/main.js"></script>

<script>
function googleTranslateElementInit() {
    new google.translate.TranslateElement({
        pageLanguage: 'en',
        includedLanguages: 'en,nl',
        autoDisplay: false
    }, 'google_translate_element');

    // After short delay, apply selected or default language
    setTimeout(() => {
        let storedLang = localStorage.getItem('selectedLanguage') || 'dutch';
        applyTranslation(storedLang);
    }, 500);
}

function applyTranslation(lang) {
    localStorage.setItem('selectedLanguage', lang);

    const interval = setInterval(() => {
        const select = document.querySelector(".goog-te-combo");
        if (select) {
            select.value = lang === "dutch" ? "nl" : "en";
            select.dispatchEvent(new Event("change"));
            clearInterval(interval);

            setTimeout(() => {
                const preloader = document.getElementById('preloader');
                if (preloader) preloader.remove();

                const content = document.getElementById('main-content');
                content.style.visibility = 'visible';
                content.style.opacity = '1';
            }, 1200);
        }
    }, 100);
}

// Language flags
document.addEventListener('DOMContentLoaded', function() {
    const eng = document.getElementById('englishFlag');
    const dutch = document.getElementById('dutchFlag');

    if (eng) eng.addEventListener('click', () => applyTranslation('english'));
    if (dutch) dutch.addEventListener('click', () => applyTranslation('dutch'));
});

// Hide Google bar
function hideGoogleTranslateBar() {
    let css = `
      .goog-te-banner-frame, .goog-te-gadget-simple {
        display: none !important;
      }
      body { top: 0px !important; }
      span, font {
        background: none !important;
        color: inherit !important;
        box-shadow: none !important;
      }
      .goog-te-spinner-pos, .VIpgJd-ZVi9od-aZ2wEe-wOHMyf {
        display: none !important;
      }
    `;
    let style = document.createElement('style');
    style.innerHTML = css;
    document.head.appendChild(style);
}

hideGoogleTranslateBar();
</script>


<!-- Google Translate Script -->
<script src="//translate.google.com/translate_a/element.js?cb=googleTranslateElementInit"></script>
</div>

<audio id="chatSound">
    <source src="<?= base_url('assets/sound/notification.mp3') ?>" type="audio/mpeg">
</audio>

<div class="toast-container position-fixed bottom-0 end-0 p-3" style="z-index: 9999;">
    <div id="chatToast" class="toast bg-success text-white" role="alert" data-bs-delay="5000">
        <div class="toast-body">
            <strong>New message received!</strong>
        </div>
    </div>
</div>


<script>
function checkUnreadChats() {
    $.ajax({
        url: "<?= base_url('chat/getUnreadCounts') ?>",
        method: "GET",
        dataType: "json",
        success: function(response) {
            let notifiedIds = JSON.parse(localStorage.getItem('notifiedMessageIds')) || [];

            response.forEach(chat => {
                if (!notifiedIds.includes(chat.id)) {
                    $('#chatSound')[0].play();
                    const toastEl = new bootstrap.Toast(document.getElementById('chatToast'));
                    toastEl.show();

                    notifiedIds.push(chat.id);
                }
            });

            localStorage.setItem('notifiedMessageIds', JSON.stringify(notifiedIds));
        }
    });
}

<?php if ($this->session->userdata('b_id') || $this->session->userdata('s_id')): ?>
setInterval(checkUnreadChats, 5000);
<?php endif; ?>
</script>
</body>

</html>
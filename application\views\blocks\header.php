<?php
    $total_balance = 0;
     if ($this->session->userdata('b_id') || $this->session->userdata('s_id')) {
        $total_balance = get_loggedin_user_balance();
    }
?>
<header id="header" class="header d-flex align-items-center sticky-top">
    <div class="container-fluid container-xl position-relative d-flex align-items-center">

        <a href="<?=base_url()?>" class="logo d-flex align-items-center me-auto">
            <!-- Uncomment the line below if you also wish to use an image logo -->
            <img src="<?=base_url()?>assets/img/logo.png" alt="">
            <!-- <h1 class="sitename">Sales Platform</h1> -->
        </a>

        <nav id="navmenu" class="navmenu">
            <ul>
                <li><a href="<?=base_url()?>" class="active">Home<br></a></li>
                <li><a href="<?=base_url()?>assignments">Assignments</a></li>
                <li><a href="<?=base_url()?>explore-freelancers">Explore Freelancers</a></li>
                <li><a href="<?=base_url()?>find-business">Find Business</a></li>
                <li><a href="<?=base_url()?>courses">Courses</a></li>
                <li><a href="<?=base_url()?>leaderboard">Leaderboard</a></li>
                <li><a href="<?=base_url()?>blogs">Blogs</a></li>


                <?php
        if(!$this->session->userdata('b_id') && !$this->session->userdata('s_id')){
            ?>
                <li><a href="<?=base_url()?>login">Login</a></li>
                <?php
        }
        ?>
                <?php
                    if($this->session->userdata('b_id')){
                ?>
                <li class="dropdown"><a><span>My Account</span> <i class="bi bi-chevron-down toggle-dropdown"></i></a>
                    <ul>
                        <li><a href="<?=base_url()?>business/profile">Profile</a></li>
                        <li><a href="<?=base_url()?>inbox">Inbox</a></li>
                        <li><a href="<?=base_url()?>business/assignment-history">Assignments</a></li>
                        <li><a href="<?=base_url()?>business/orders">My Orders</a></li>
                        <li><a href="<?=base_url()?>course/my_courses">My Courses</a></li>
                        <li><a href="<?=base_url()?>withdraw">Withdrawal</a></li>
                        <li><a href="<?=base_url()?>referrel-program-explanation">Referral Program Explanation</a></li>
                        <li><a href="<?=base_url()?>profile-promotion-explanation">Profile Promotion Explanation</a></li>
                        <li><a href="<?=base_url()?>logout">Logout</a></li>
                        <hr>
                        <li><a>Total Balance: <?="$ ".number_format($total_balance, 2)?></a></li>
                    </ul>
                </li>
                <?php
                    }
                ?>

                <?php
                    if($this->session->userdata('s_id')){
                ?>
                <li class="dropdown"><a><span>My Account</span> <i class="bi bi-chevron-down toggle-dropdown"></i></a>
                    <ul>
                        <li><a href="<?=base_url()?>sales/profile">Profile</a></li>
                        <li><a href="<?=base_url()?>inbox">Inbox</a></li>
                        <li><a href="<?=base_url()?>sales/bid-history">Applications</a></li>
                        <li><a href="<?=base_url()?>sales/orders">My Assignments</a></li>
                        <li><a href="<?=base_url()?>withdraw">Withdrawal</a></li>
                        <li><a href="<?=base_url()?>sales/earnings">Earnings</a></li>
                        <li><a href="<?=base_url()?>course/my_courses">My Courses</a></li>
                        <li><a href="<?=base_url()?>course/enrollment_history">Enrollment History</a></li>
                        <li><a href="<?=base_url()?>referrel-program-explanation">Referrel Program Explanation</a></li>
                        <li><a href="<?=base_url()?>profile-promotion-explanation">Profile Promotion Explanation</a></li>
                        <hr>
                        <li><a>Total Balance: <?="$ ".number_format($total_balance, 2)?></a></li>
                        <li><a href="<?=base_url()?>logout">Logout</a></li>
                    </ul>
                </li>
                <?php
                    }
                ?>
            </ul>
            <i class="mobile-nav-toggle d-xl-none bi bi-list"></i>
        </nav>
        <?php
        if($this->session->userdata('b_id') || $this->session->userdata('s_id')){
        ?>
        <a class="btn-getstarted" href="<?=base_url()?>logout">Logout</a>
        <?php
        }
        else{
            ?>
        <a class="btn-getstarted" href="<?=base_url()?>register">Get Started</a>
        <?php
        }
        ?>

    </div>
</header>
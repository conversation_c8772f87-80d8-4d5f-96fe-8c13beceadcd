<main class="main">

    <!-- Page Title -->
    <div class="page-title" data-aos="fade">
        <div class="heading">
            <div class="container">
                <div class="row d-flex justify-content-center text-center">
                    <div class="col-lg-8">
                        <h1>Assignment Applications<br></h1>
                        <p class="mb-0 mt-3">
                        </p>
                    </div>
                </div>
            </div>
        </div>
        <nav class="breadcrumbs">
            <div class="container">
                <ol>
                    <li><a href="<?= base_url() ?>">Home</a></li>
                    <li class="current">Assignment Applications<br></li>
                </ol>
            </div>
        </nav>
    </div><!-- End Page Title -->
    <div class="container mt-5">
        <h3 class="text-center mb-4">Applications for Assignment: <?= $assignment->title; ?></h3>

        <?php if (empty($bids)): ?>
        <div class="alert alert-info text-center" role="alert">
            No Applications placed on this assignment yet.
        </div>
        <?php else: ?>
        <div class="row">
            <?php foreach ($bids as $bid): ?>
            <div class="col-md-12">
                <div class="card mb-3">
                    <div class="card-body">
                        <h5 class="card-title">
                            <a href='<?= base_url() ?>freelancer-details/<?= $bid->sales_professional_id ?>'>
                                <b><?= $bid->sales_professional_name; ?></b>
                            </a>
                        </h5>
                        <p class="card-text"><?= $bid->description; ?></p>
                        <p class="card-text"><small class="text-muted">Application Date:
                                <?= date('d M Y, H:i', strtotime($bid->created_at)); ?></small></p>
                        <p class="card-text">
                            <?php
                        if ($bid->payment_type == 'hourly') {
                            echo "<small class='text-muted'>Hourly Rate: $" . $bid->hourly_rate . "</small>";
                        } elseif ($bid->payment_type == 'hourly_bonus') {
                            echo "<small class='text-muted'>Hourly Rate: $" . $bid->hourly_rate . " + Bonus</small>";
                        } else {
                            echo "<small class='text-muted'>Commission Based</small>";
                        }
                    ?>
                        </p>
                        <p class="card-text">
                            <button class="btn btn-outline-success chat-btn"
                                data-id="<?= $bid->sales_professional_id ?>">Chat</button>
                            <button class="btn btn-success accept-bid-btn" data-bid-id="<?= $bid->id ?>"
                                data-user-id="<?= $bid->sales_professional_id ?>"
                                data-hourly-rate="<?= $bid->hourly_rate ?>"
                                data-payment-type="<?= $bid->payment_type ?>">
                                Accept
                            </button>
                        </p>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>


        </div>
        <?php endif; ?>
        <div class="mt-4">
            <?= $pagination; ?>
        </div>
    </div>

    <!-- Stripe Payment Modal -->
    <div class="modal fade" id="paymentModal" tabindex="-1" aria-labelledby="paymentModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="paymentModalLabel">Enter Payment Details</h5>

                </div>
                <div class="modal-body">
                    <form id="payment-form">
                        <input type="hidden" id="bid_id">
                        <input type="hidden" id="user_id">
                        <input type="hidden" id="bid_amount">
                        <div class="mb-3">
                            <label for="card_number" class="form-label">Card Number *</label>
                            <div id="card_number" class='form-control' class="StripeElement"></div>
                        </div>
                        <div class="mb-3">
                            <label for="expiry_date" class="form-label">Expiry Date *</label>
                            <div id="expiry_date" class='form-control' class="StripeElement"></div>
                        </div>
                        <div class="mb-3">
                            <label for="cvv" class="form-label">CVC *</label>
                            <div id="cvv" class='form-control' class="StripeElement"></div>
                        </div>
                        <button type="submit" class="btn btn-success mt-3">Pay Now</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="chatModal" tabindex="-1" aria-labelledby="chatModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="chatModalLabel">Start Chat</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <textarea class="form-control" id="chatMessage" rows="3"
                        placeholder="Type your message..."></textarea>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-success" id="sendChatMessage">Send Message</button>
                </div>
            </div>
        </div>
    </div>

</main>


<script src="https://js.stripe.com/v3/"></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>


<script>
$(document).ready(function() {
    let selectedUserId = null; // Store the user ID for chat
    let businessId = "<?= $this->session->userdata('b_id'); ?>"; // Logged-in business user ID

    // Open chat modal
    $(".chat-btn").click(function() {
        selectedUserId = $(this).data("id");
        $("#chatModal").modal("show");
    });

    // Send message
    $("#sendChatMessage").click(function() {
        let message = $("#chatMessage").val().trim();
        if (message === "") {
            alert("Please enter a message.");
            return;
        }

        $.ajax({
            url: "<?= base_url('Chat/start_chat') ?>",
            type: "POST",
            data: {
                business_id: businessId,
                sales_professional_id: selectedUserId,
                message: message
            },
            dataType: "json",
            success: function(response) {
                $("#chatModal").modal("hide");
                if (response.status === "exists") {
                    window.location.href = "<?= base_url() ?>/inbox/" + response.chat_id;
                } else if (response.status === "new") {
                    Swal.fire({
                        title: "Message Sent!",
                        text: "Your chat has been started successfully.",
                        icon: "success",
                        confirmButtonText: "Go to Chat"
                    }).then(() => {
                        window.location.href = "<?= base_url() ?>inbox/" + response
                            .chat_id;
                    });
                } else {
                    alert("Something went wrong. Please try again.");
                }
            }
        });
    });
});
</script>


<script>
$(document).ready(function() {

    var stripe = Stripe(
        'pk_live_51QlU9YGj7vd04uS6VZDoWcCJ7GLQleVHrKfnHOF9yxup31WypzqdS0be1n3n5B6zMCUiGrJvy0gD8uFEbekfHEE000HRvEa6Ie'
    );
    var elements = stripe.elements();

    var style = {
        base: {
            color: '#32325d',
            fontFamily: '"Helvetica Neue", Helvetica, sans-serif',
            fontSmoothing: 'antialiased',
            fontSize: '16px',
            '::placeholder': {
                color: '#aab7c4'
            }
        },
        invalid: {
            color: '#fa755a',
            iconColor: '#fa755a'
        }
    };

    var cardNumber = elements.create('cardNumber', {
        style: style
    });
    var cardExpiry = elements.create('cardExpiry', {
        style: style
    });
    var cardCvc = elements.create('cardCvc', {
        style: style
    });

    cardNumber.mount('#card_number');
    cardExpiry.mount('#expiry_date');
    cardCvc.mount('#cvv');

    $(".accept-bid-btn").click(function() {
        let bidId = $(this).data("bid-id");
        let userId = $(this).data("user-id");
        let hourlyRate = $(this).data("hourly-rate");
        let paymentType = $(this).data("payment-type");

        if (paymentType === "result_based") {
            Swal.fire({
                title: "Accept Application?",
                text: "This is a result-based agreement. You won't be charged now.",
                icon: "info",
                showCancelButton: true,
                confirmButtonText: "Yes, Accept",
                cancelButtonText: "Cancel"
            }).then((result) => {
                if (result.isConfirmed) {
                    $.ajax({
                        url: "<?= base_url('orders/accept_result_based') ?>", // You handle this in your controller
                        type: "POST",
                        data: {
                            bid_id: bidId,
                            user_id: userId
                        },
                        success: function(res) {
                            Swal.fire("Accepted",
                                    "You have accepted the application.", "success")
                                .then(() => {
                                    location.reload();
                                });
                        }
                    });
                }
            });

        } else {
            // hourly or hourly_bonus — show payment modal
            $("#bid_id").val(bidId);
            $("#user_id").val(userId);
            $("#bid_amount").val(hourlyRate); // use hourlyRate instead of bid amount
            Swal.fire({
                title: "Are you sure?",
                text: "You are about to accept this bid and proceed to payment.",
                icon: "warning",
                showCancelButton: true,
                confirmButtonText: "Yes, Pay Now!",
                cancelButtonText: "Cancel"
            }).then((result) => {
                if (result.isConfirmed) {
                    $("#paymentModal").modal("show");
                }
            });
        }
    });


    $("#payment-form").submit(function(event) {
        event.preventDefault();
        Swal.fire({
            title: "Processing Payment...",
            html: "Please wait while we process your payment.",
            allowOutsideClick: false,
            showConfirmButton: false,
            willOpen: () => {
                Swal.showLoading();
            }
        });

        stripe.createPaymentMethod({
            type: "card",
            card: cardNumber,
            billing_details: {
                name: `<?= $this->session->userdata('name')?>`,
                email: `<?= $this->session->userdata('email')?>`,
            }
        }).then(function(result) {
            if (result.error) {
                Swal.fire("Error!", result.error.message, "error");
            } else {
                $.ajax({
                    url: "<?= base_url('orders/process_payment') ?>",
                    type: "POST",
                    data: {
                        bid_id: $("#bid_id").val(),
                        user_id: $("#user_id").val(),
                        amount: $("#bid_amount").val(),
                        payment_method_id: result.paymentMethod.id
                    },
                    dataType: "json",
                    success: function(response) {
                        if (response.requires_action) {
                            handleAuthentication(response
                                .payment_intent_client_secret);
                        } else if (response.success === true) {
                            Swal.fire("Success!",
                                    "Payment Successful. Order Placed!", "success")
                                .then(() => {
                                    location.reload();
                                });
                        } else {
                            Swal.fire("Error!", response.message, "error");
                        }
                    }
                });
            }
        });
    });

    function handleAuthentication(clientSecret) {
        stripe.confirmCardPayment(clientSecret).then(function(result) {
            if (result.error) {
                Swal.close();
                Swal.fire("Error!", result.error.message, "error");
            } else if (result.paymentIntent.status === 'succeeded') {
                $.ajax({
                    url: "<?= base_url('orders/confirm_payment') ?>",
                    method: 'post',
                    data: {
                        payment_intent_id: result.paymentIntent.id
                    },
                    dataType: "json",
                    success: function(response) {
                        if (response.success) {
                            Swal.close();
                            Swal.fire("Success!",
                                "Payment Successful. Order Placed!", "success")
                        }
                    }
                });
            } else {
                Swal.close();
                Swal.fire("Error!", "Payment failed. Please try again.", "error");
            }
        });
    }
});
</script>
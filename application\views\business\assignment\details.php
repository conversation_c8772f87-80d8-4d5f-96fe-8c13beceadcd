<main class="main">

    <!-- Page Title -->
    <div class="page-title" data-aos="fade">
        <div class="heading">
            <div class="container">
                <div class="row d-flex justify-content-center text-center">
                    <div class="col-lg-8">
                        <h1>Assignment Details</h1>
                    </div>
                </div>
            </div>
        </div>
        <nav class="breadcrumbs">
            <div class="container">
                <ol>
                    <li><a href="<?=base_url()?>">Home</a></li>
                    <li class="current">Assignment Details</li>
                </ol>
            </div>
        </nav>
    </div><!-- End Page Title -->

    <!-- Courses Course Details Section -->
    <section id="courses-course-details" class="courses-course-details section">

        <div class="container" data-aos="fade-up">

            <div class="row">
                <div class="col-lg-8">
                    <img src="<?= base_url('assets/img/assignments/banners/' . $assignment->banner); ?>"
                        class="img-fluid" alt="">
                    <h3><?=$assignment->title?></h3>
                    <p>
                        <?=$assignment->description?>
                    </p>
                </div>
                <div class="col-lg-4">

                    <div class="course-info d-flex justify-content-between align-items-center">
                        <h5>Posted on</h5>
                        <p><?= time_ago($assignment->created_at); ?></p>
                    </div>

                    <div class="course-info d-flex justify-content-between align-items-center">
                        <h5>Budget</h5>
                        <p>$<?=$assignment->budget?></p>
                    </div>

                    <?php
                    if($assignment->pricing_type && !empty($assignment->pricing_type)){
                    ?>
                    <div class="mb-2">
                        <strong>Payment Type:</strong>
                        <?php if ($assignment->pricing_type === 'hourly'): ?>
                        Hourly ($<?= $assignment->hourly_rate ?>)
                        <?php elseif ($assignment->pricing_type === 'hourly_bonus'): ?>
                        Hourly ($<?= $assignment->hourly_rate ?>) + Bonus
                        <br><strong>Bonus:</strong> <?= $assignment->bonus_details ?>
                        <?php elseif ($assignment->pricing_type === 'commission'): ?>
                        Commission-Based
                        <br><strong>Structure:</strong> <?= $assignment->commission_details ?>
                        <?php endif; ?>
                    </div>
                    <?php
                    }
                    ?>

                    <div class="course-info d-flex justify-content-between align-items-center">
                        <h5>Deadline</h5>
                        <p><?=$assignment->deadline?></p>
                    </div>

                    <div class="course-info">

                        <a href="<?= base_url('business/assignmentEdit/' . $assignment->id); ?>"
                            class="btn btn-success d-block w-100 text-white">
                            <i class='fa fa-pencil-o'></i> Edit Assignment
                        </a>

                        <button class="btn btn-outline-success d-block w-100 mb-2" type="button"
                                            onclick="openPromotionModal('company_job', <?= $assignment->id?>)">Promote</button>


                    </div>

                </div>
            </div>

        </div>

    </section>

</main>

<div class="modal fade" id="promotionModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <form id="promotionForm" method="post">
            <input type="hidden" name="promotion_type" id="promotion_type">
            <input type="hidden" name="reference_id" id="reference_id">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Choose Promotion Package</h5>
                </div>
                <div class="modal-body">
                    <?php foreach ($promotion_settings as $setting): ?>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="setting_id" value="<?= $setting->id ?>"
                            required>
                        <label class="form-check-label">
                            <?= ucfirst(str_replace('_', ' ', $setting->type)) ?> - <?= $setting->duration_days ?> days
                            - $<?= $setting->price ?>
                        </label>
                    </div>
                    <?php endforeach; ?>
                    <div class="mb-3">
                        <label for="card_number" class="form-label">Card Number *</label>
                        <div id="card_number" class='form-control' class="StripeElement"></div>
                    </div>
                    <div class="mb-3">
                        <label for="expiry_date" class="form-label">Expiry Date *</label>
                        <div id="expiry_date" class='form-control' class="StripeElement"></div>
                    </div>
                    <div class="mb-3">
                        <label for="cvv" class="form-label">CVC *</label>
                        <div id="cvv" class='form-control' class="StripeElement"></div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="submit" class="btn btn-success">Continue to Payment</button>
                </div>
            </div>
        </form>
    </div>
</div>

<script src="https://js.stripe.com/v3/"></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script>
function openPromotionModal(type, referenceId) {
    $('#promotion_type').val(type);
    $('#reference_id').val(referenceId);
    $('#promotionModal').modal('show');
}
</script>

<script>
$(document).ready(function () {
    const stripe = Stripe('pk_live_51QlU9YGj7vd04uS6VZDoWcCJ7GLQleVHrKfnHOF9yxup31WypzqdS0be1n3n5B6zMCUiGrJvy0gD8uFEbekfHEE000HRvEa6Ie');
    const elements = stripe.elements();

    const style = {
        base: {
            color: '#32325d',
            fontFamily: '"Helvetica Neue", Helvetica, sans-serif',
            fontSmoothing: 'antialiased',
            fontSize: '16px',
            '::placeholder': { color: '#aab7c4' }
        },
        invalid: {
            color: '#fa755a',
            iconColor: '#fa755a'
        }
    };

    const cardNumber = elements.create('cardNumber', { style });
    const cardExpiry = elements.create('cardExpiry', { style });
    const cardCvc = elements.create('cardCvc', { style });

    cardNumber.mount('#card_number');
    cardExpiry.mount('#expiry_date');
    cardCvc.mount('#cvv');

    $("#promotionForm").on("submit", function (e) {
        e.preventDefault();

        Swal.fire({
            title: "Confirm Promotion",
            text: "Do you want to proceed with this promotion package?",
            icon: "question",
            showCancelButton: true,
            confirmButtonText: "Yes, Proceed",
        }).then((result) => {
            if (result.isConfirmed) {
                Swal.fire({
                    title: "Processing Payment...",
                    html: "Please wait while your payment is processed.",
                    allowOutsideClick: false,
                    showConfirmButton: false,
                    willOpen: () => {
                        Swal.showLoading();
                    }
                });

                const selectedPackageId = $("input[name='setting_id']:checked").val();
                const promotionType = $("#promotion_type").val();
                const referenceId = $("#reference_id").val();

                stripe.createPaymentMethod({
                    type: "card",
                    card: cardNumber,
                    billing_details: {
                        name: `<?= $this->session->userdata('name')?>`,
                        email: `<?= $this->session->userdata('email')?>`,
                    }
                }).then(function (result) {
                    if (result.error) {
                        Swal.fire("Error!", result.error.message, "error");
                    } else {
                        $.ajax({
                            url: "<?= base_url('promotion/order') ?>",
                            type: "POST",
                            data: {
                                setting_id: selectedPackageId,
                                promotion_type: promotionType,
                                reference_id: referenceId,
                                payment_method_id: result.paymentMethod.id
                            },
                            dataType: "json",
                            success: function (response) {
                                if (response.requires_action) {
                                    handlePromotionAuthentication(response.payment_intent_client_secret);
                                } else if (response.success === true) {
                                    Swal.fire("Success!", "Payment Successful!", "success")
                                        .then(() => { location.reload(); });
                                } else {
                                    Swal.fire("Error!", response.message, "error");
                                }
                            }
                        });
                    }
                });
            }
        });
    });

    function handlePromotionAuthentication(clientSecret) {
        stripe.confirmCardPayment(clientSecret).then(function (result) {
            if (result.error) {
                Swal.close();
                Swal.fire("Error!", result.error.message, "error");
            } else if (result.paymentIntent.status === 'succeeded') {
                $.post("<?= base_url('promotion/confirm_payment') ?>", {
                    payment_intent_id: result.paymentIntent.id
                }, function (resp) {
                    if (resp.success) {
                        Swal.fire("Success!", "Promotion Applied Successfully", "success")
                            .then(() => location.reload());
                    } else {
                        Swal.fire("Error!", resp.error, "error");
                    }
                }, 'json');
            }
        });
    }
});
</script>
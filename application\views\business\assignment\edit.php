<main class="main">
    <!-- Page Title -->
    <div class="page-title" data-aos="fade">
        <div class="heading">
            <div class="container">
                <div class="row d-flex justify-content-center text-center">
                    <div class="col-lg-8">
                        <h1>Edit Assignment<br></h1>
                        <p class="mb-0">Odio et unde deleniti. Deserunt numquam exercitationem. Officiis quo odio sint
                            voluptas consequatur ut a odio voluptatem. Sit dolorum debitis veritatis natus dolores.
                            Quasi ratione sint. Sit quaerat ipsum dolorem.</p>
                    </div>
                </div>
            </div>
        </div>
        <nav class="breadcrumbs">
            <div class="container">
                <ol>
                    <li><a href="<?=base_url()?>">Home</a></li>
                    <li class="current">Edit Assingment<br></li>
                </ol>
            </div>
        </nav>
    </div><!-- End Page Title -->
    <div class="container mt-5">
        <div class="row">
            <div class="col-md-8 offset-md-2">
                <h3 class="text-center">Edit Assignment</h3>
                <?= form_open_multipart('business/assignmentEdit/' . $assignment->id); ?>



                <div class="form-group">
                    <label for="title">Assignment Title</label>
                    <input type="text" class="form-control" id="title" name="title" value="<?= $assignment->title; ?>"
                        required>
                </div>
                <div class="form-group mt-3">
                    <label for="description">Description</label>
                    <textarea class="form-control" id="description" name="description" rows="4"
                        required><?= $assignment->description; ?></textarea>
                </div>
                <div class="form-group mt-3">
                    <label for="budget">Budget</label>
                    <input type="number" class="form-control" id="budget" name="budget"
                        value="<?= $assignment->budget; ?>">
                </div>

                <div class="form-group mt-3">
                    <label class='form-label'>Choose Your Pricing Type</label>
                    <select name="pricing_type" id="pricing_type" class="form-control" required>
                        <option value="">Select Pricing Type</option>
                        <option <?= $assignment->pricing_type == "hourly" ? "selected":""; ?> value="hourly">Hourly</option>
                        <option <?= $assignment->pricing_type == "hourly_bonus" ? "selected":""; ?> value="hourly_bonus">Hourly + Bonus</option>
                        <option <?= $assignment->pricing_type == "result_based" ? "selected":""; ?> value="result_based">Result-based (No Cure, No Pay)</option>
                    </select>
                </div>

                <div class="form-group mt-3">
                    <div id="hourly_fields"
                        style="<?=$assignment->pricing_type == "hourly" || $assignment->pricing_type == "hourly_bonus" ? "":"display:none;"?>">
                        <label class='form-label'>Hourly Rate ($):</label>
                        <input type="number" step="0.01" name="hourly_rate"
                            value="<?= $assignment->hourly_rate; ?>" class="form-control">
                    </div>

                </div>
                <div class="form-group mt-3">
                    <div id="bonus_fields"
                        style="<?=$assignment->pricing_type == "hourly_bonus"? "":"display:none;"?>">
                        <label class='form-label'>Bonus Details:</label>
                        <textarea name="bonus_details"
                            class="form-control"><?= $assignment->bonus_details; ?></textarea>
                    </div>
                </div>

                <div class="form-group mt-3">
                    <div id="commission_fields"
                        style="<?=$assignment->pricing_type == "result_based"? "":"display:none;"?>">
                        <label class='form-label'>Commission Structure:</label>
                        <textarea name="commission_details"
                            class="form-control"><?= $assignment->commission_details; ?></textarea>
                    </div>
                </div>

                <div class="form-group mt-3">
                    <label class='form-label'>Select Payment Method</label>
                    <select name="payment_method" id="payment_method" class="form-control" required>
                        <option value="">Select Payment Method</option>
                        <option <?= $assignment->payment_method == "upfront" ? "selected":""; ?> value="upfront">Upfront Payment</option>
                        <option <?= $assignment->payment_method == "post_payment" ? "selected":""; ?> value="post_payment">Post-payment</option>
                    </select>
                </div>

                <div class="form-group mt-3">
                    <label for="deadline">Deadline</label>
                    <input type="date" class="form-control" id="deadline" name="deadline"
                        value="<?= $assignment->deadline; ?>" required>
                </div>
                <div class="form-group mt-3">
                    <label for="file">Upload File (Optional)</label>
                    <input type="file" class="form-control-file" id="file" name="file">
                    <?php if ($assignment->file): ?>
                    <p>Current File: <a
                            href="<?= base_url('assets/img/assignments/attachments/' . $assignment->file); ?>"
                            target="_blank"><?= $assignment->file; ?></a></p>
                    <?php endif; ?>
                </div>
                <div class="form-group mt-3">
                    <label for="banner">Banner</label>
                    <input type="file" class="form-control" id="banner" name="banner">
                    <?php if ($assignment->banner): ?>
                    <p>Current Banner: <a
                            href="<?= base_url('assets/img/assignments/banners/' . $assignment->banner); ?>"
                            target="_blank"><?= $assignment->banner; ?></a></p>
                    <?php endif; ?>
                </div>

                <div class="form-group mt-3">
                    <label>
                        <input type="checkbox" id="is_public" name="is_public" value="1"
                            <?= ($assignment->is_public == 1)?"checked":"" ?>>
                        Show to Public (Visitors)
                    </label>
                </div>

                <button type="submit" class="btn btn-success w-100 d-block mt-3">Save Changes</button>
                <?= form_close(); ?>
            </div>
        </div>
    </div>
</main>


<script>
$('#pricing_type').on('change', function() {
    const val = $(this).val();
    $('#hourly_fields, #bonus_fields, #commission_fields').hide();
    $('#payment_method_group').show();

    if (val === 'hourly') {
        $('#hourly_fields').show();
    } else if (val === 'hourly_bonus') {
        $('#hourly_fields, #bonus_fields').show();
    } else if (val === 'result_based') {
        $('#commission_fields').show();
    }
}).trigger('change');
</script>
<main class="main">
    <!-- Page Title -->
    <div class="page-title" data-aos="fade">
        <div class="heading">
            <div class="container">
                <div class="row d-flex justify-content-center text-center">
                    <div class="col-lg-8">
                        <h1>My Assignments<br></h1>
                        <p class="mb-0 mt-3">
                            <a class='btn btn-outline-secondary border-white text-white'
                                href="<?= base_url() ?>business/assignmentUpload">
                                Post an Assignment
                            </a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
        <nav class="breadcrumbs">
            <div class="container">
                <ol>
                    <li><a href="<?= base_url() ?>">Home</a></li>
                    <li class="current">Assignments<br></li>
                </ol>
            </div>
        </nav>
    </div><!-- End Page Title -->

    <section id="courses" class="courses section">

        <div class="container mt-5">
            <h3 class="text-center mb-4">Assignments</h3>

            <?php if (empty($assignments)): ?>
            <div class="alert alert-danger text-center" role="alert">
                No assignments available at the moment.
            </div>
            <?php else: ?>
            <div class="row">
                <?php foreach ($assignments as $assignment): ?>




                <div class="col-lg-4 col-md-6 d-flex align-items-stretch mt-4 mt-lg-0" data-aos="zoom-in"
                    data-aos-delay="300">
                    <div class="course-item">
                        <img src="<?= base_url('assets/img/assignments/banners/' . $assignment->banner); ?>"
                            class="img-fluid" alt="...">
                        <div class="course-content">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <p class="category"><?= time_ago($assignment->created_at); ?></p>
                                <p class="price">$<?=$assignment->budget?></p>
                            </div>

                            <h3><?=$assignment->title?></h3>
                            <p class="description"><?=substr($assignment->description, 0, 250);?></p>
                            <?php
                    if($assignment->pricing_type && !empty($assignment->pricing_type)){
                    ?>
                            <div class="mb-2">
                                <strong>Payment Type:</strong>
                                <?php if ($assignment->pricing_type === 'hourly'): ?>
                                Hourly ($<?= $assignment->hourly_rate ?>)
                                <?php elseif ($assignment->pricing_type === 'hourly_bonus'): ?>
                                Hourly ($<?= $assignment->hourly_rate ?>) + Bonus
                                <br><strong>Bonus:</strong> <?= $assignment->bonus_details ?>
                                <?php elseif ($assignment->pricing_type === 'commission'): ?>
                                Commission-Based
                                <br><strong>Structure:</strong> <?= $assignment->commission_details ?>
                                <?php endif; ?>
                            </div>
                            <?php
                    }
                    ?>
                            <p class="card-text">
                                <a href="<?= base_url('business/assignmentBids/' . $assignment->id); ?>"
                                    class="text-success">
                                    Applications: <?= $assignment->bid_count; ?>
                                </a>
                            </p>
                            <div class="trainer">
                                <a class='btn btn-success w-100 d-block'
                                    href="<?=base_url('business/assignmentDetails/'.$assignment->id)?>">View Details</a>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- </div> -->
                <?php endforeach; ?>
            </div>
            <?php endif; ?>

            <div class="mt-4">
                <?= $this->pagination->create_links(); ?>
            </div>
        </div>
    </section>
</main>
<main class="main">
    <!-- Page Title -->
    <div class="page-title" data-aos="fade">
        <div class="heading">
            <div class="container">
                <div class="row d-flex justify-content-center text-center">
                    <div class="col-lg-8">
                        <h1>Post an Assignment<br></h1>
                    </div>
                </div>
            </div>
        </div>
        <nav class="breadcrumbs">
            <div class="container">
                <ol>
                    <li><a href="<?=base_url()?>">Home</a></li>
                    <li class="current">Post Assingment<br></li>
                </ol>
            </div>
        </nav>
    </div><!-- End Page Title -->
    <div class="container mt-5">
        <div class="row">
            <div class="col-md-8 offset-md-2">
                <h3 class="text-center">Post an Assignment</h3>
                <?= form_open_multipart('business/assignmentUpload'); ?>
                <div class="form-group">
                    <label for="banner">Banner</label>
                    <input type="file" class="form-control" id="banner" name="banner" required>
                </div>

                <div class="form-group mt-3">
                    <label for="title">Assignment Title</label>
                    <input type="text" class="form-control" id="title" name="title" placeholder="Enter assignment title"
                        required>
                </div>
                <div class="form-group mt-3">
                    <label for="description">Description</label>
                    <textarea class="form-control" id="description" name="description" rows="4"
                        placeholder="Enter assignment description" required></textarea>
                </div>
                <div class="form-group mt-3" id="budget">
                    <label for="budget">Budget</label>
                    <input type="number" class="form-control" id="budget" name="budget" placeholder="Enter budget">
                </div>

                <div class="form-group mt-3">
                    <label class='form-label'>Choose Your Pricing Type</label>
                    <select name="pricing_type" id="pricing_type" class="form-control" required>
                        <option value="">Select Pricing Type</option>
                        <option value="hourly">Hourly</option>
                        <option value="hourly_bonus">Hourly + Bonus</option>
                        <option value="result_based">Result-based (No Cure, No Pay)</option>
                    </select>
                </div>

                <div class="form-group mt-3" id="hourly_fields" style="display:none;">
                    <label class='form-label'>Hourly Rate ($):</label>
                    <input type="number" step="0.01" name="hourly_rate" class="form-control">
                </div>

                <div class="form-group mt-3" id="bonus_fields" style="display:none;">
                    <label class='form-label'>Bonus Details:</label>
                    <textarea name="bonus_details" class="form-control"></textarea>
                </div>

                <div class="form-group mt-3" id="commission_fields" style="display:none;">
                    <label class='form-label'>Result Expectations / Commission Terms:</label>
                    <textarea name="commission_details" class="form-control"></textarea>
                </div>



                <div class="form-group mt-3">
                    <label class='form-label'>Select Payment Method</label>
                    <select name="payment_method" id="payment_method" class="form-control" required>
                        <option value="">Select Payment Method</option>
                        <option value="upfront">Upfront Payment</option>
                        <option value="post_payment">Post-payment</option>
                    </select>
                </div>


                <div class="form-group mt-3">
                    <label for="deadline">Deadline</label>
                    <input type="date" class="form-control" id="deadline" name="deadline" required>
                </div>
                <div class="form-group mt-3">
                    <label for="file">Upload File (Optional)</label>
                    <input type="file" class="form-control" id="file" name="file">
                </div>
                <div class="form-group mt-3">
                    <label>
                        <input type="checkbox" id="is_public" name="is_public" value="1">
                        Show to Public (Visitors)
                    </label>
                </div>

                <button type="submit" class="btn btn-success w-100 d-block mt-3 mb-5">Submit</button>
                <?= form_close(); ?>
            </div>
        </div>
    </div>

</main>

<script>
$('#pricing_type').on('change', function() {
    const val = $(this).val();
    $('#hourly_fields, #bonus_fields, #commission_fields').hide();
    $('#payment_method_group').show();

    if (val === 'hourly') {
        $('#hourly_fields').show();
    } else if (val === 'hourly_bonus') {
        $('#hourly_fields, #bonus_fields').show();
    } else if (val === 'result_based') {
        $('#commission_fields').show();
    }
}).trigger('change');
</script>
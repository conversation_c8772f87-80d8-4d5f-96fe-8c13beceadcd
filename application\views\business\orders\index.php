<main>
    <div class="page-title" data-aos="fade">
        <div class="heading">
            <div class="container">
                <div class="row d-flex justify-content-center text-center">
                    <div class="col-lg-8">
                        <h1>My Order History</h1>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="section">
        <div class="container my-5">
            <h2 class="text-center mb-4">My Orders</h2>
            <ul class="nav nav-tabs" id="orderTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="in-progress-tab" data-bs-toggle="tab"
                        data-bs-target="#in-progress" type="button" role="tab">In Progress</button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="completed-tab" data-bs-toggle="tab" data-bs-target="#completed"
                        type="button" role="tab">Completed</button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="cancelled-tab" data-bs-toggle="tab" data-bs-target="#cancelled"
                        type="button" role="tab">Cancelled</button>
                </li>
            </ul>
            <div class="tab-content mt-4" id="orderTabsContent">
                <?php 
                $statuses = ['in_progress' => $in_progress, 'completed' => $completed, 'cancelled' => $cancelled];
                foreach ($statuses as $status => $orders):?>
                <div class="tab-pane fade <?= $status == 'in_progress' ? 'show active' : ''; ?>" id="<?= $status; ?>"
                    role="tabpanel">
                    <?php if (empty($orders)): ?>
                    <div class="alert alert-info">No order history.</div>
                    <?php else: ?>
                    <?php foreach ($orders as $order): ?>
                    <div class="card mb-3 p-3 border d-flex flex-row align-items-center">
                        <div class="me-3 text-center">
                            <img src="<?= base_url('assets/img/sales/'.$order->sales_professional_image); ?>"
                                class="rounded-circle" width="80" height="80"
                                alt="<?= $order->sales_professional_name; ?>">
                            <p><?= $order->sales_professional_name; ?></p>
                        </div>
                        <div class="flex-grow-1">
                            <h5 class="card-title"><?= $order->title; ?></h5>
                            <?php
                                    if($order->amount !== NULL){
                                    ?>
                            <p class="card-text">Budget: $<?= $order->amount; ?></p>
                            <?php
                                    }
                                    else{
                                        ?>
                            <p class="card-text">Budget: will be decided on result based</p>
                            <?php
                                    }
                                    ?>
                        </div>
                        <div class="text-end">
                            <p>Deadline: <?= date('d M Y', strtotime($order->deadline)); ?></p>
                            <?php if ($status == 'in_progress'): ?>
                            <!-- Check if delivery is not empty -->
                            <?php if (!empty($order->delivery)): ?>
                            <a href="<?= base_url('assets/deliveries/'.$order->delivery); ?>"
                                class="btn btn-sm btn-outline-info" download>
                                <i class="fa fa-download"></i> Download Delivery
                            </a>
                            <?php endif; ?>
                            <button class="btn btn-sm btn-outline-success complete-btn"
                                data-amount="<?=$order->amount?>" data-id="<?= $order->id; ?>">Mark as Complete</button>
                            <button class="btn btn-sm btn-outline-danger cancel-btn" data-id="<?= $order->id; ?>">Cancel
                                Order</button>
                            <?php elseif ($status == 'completed'): ?>
                            <!-- Check if review is given or not -->
                            <?php if (empty($order->review_id)): ?>
                            <!-- No review given yet -->
                            <button class="btn btn-success btn--sm review-btn"
                                data-sp="<?=$order->sales_professional_id?>" data-id="<?= $order->id; ?>">Leave a
                                Review</button>
                            <?php else: ?>
                            <!-- Review already submitted -->
                            <p class="text-success">Review Submitted</p>
                            <?php endif; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                    <?php endforeach; ?>
                    <?php endif; ?>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
</main>



<!-- Review Modal -->
<div class="modal fade" id="reviewModal" tabindex="-1" aria-labelledby="reviewModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="reviewModalLabel">Leave a Review</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="reviewForm">
                    <input type="hidden" name="order_id" id="order_id">
                    <input type="hidden" id="rating-value" name="rating" value="0">

                    <div class="mb-3">
                        <label class="form-label">Rating</label>
                        <div class="star-rating">
                            <i class="fa fa-star-o" data-rating="1"></i>
                            <i class="fa fa-star-o" data-rating="2"></i>
                            <i class="fa fa-star-o" data-rating="3"></i>
                            <i class="fa fa-star-o" data-rating="4"></i>
                            <i class="fa fa-star-o" data-rating="5"></i>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="review-text" class="form-label">Review</label>
                        <textarea class="form-control" id="review-text" name="review" rows="4" required></textarea>
                    </div>
                    <button type="button" class="btn btn-success" id="submitReview">Submit Review</button>
                </form>
            </div>
        </div>
    </div>
</div>


<div class="modal fade" id="paymentModal" tabindex="-1" aria-labelledby="paymentModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="paymentModalLabel">Enter Payment Details</h5>

            </div>
            <div class="modal-body">
                <form id="payment-form">
                    <input type="hidden" id="payment_order_id">
                    <div class="mb-3">
                        <label for="amount" class="form-label">Amount *</label>
                        <input type="number" id='payment_amount' placeholder="Enter Result Based Amount" required>
                    </div>
                    <div class="mb-3">
                        <label for="card_number" class="form-label">Card Number *</label>
                        <div id="card_number" class='form-control' class="StripeElement"></div>
                    </div>
                    <div class="mb-3">
                        <label for="expiry_date" class="form-label">Expiry Date *</label>
                        <div id="expiry_date" class='form-control' class="StripeElement"></div>
                    </div>
                    <div class="mb-3">
                        <label for="cvv" class="form-label">CVC *</label>
                        <div id="cvv" class='form-control' class="StripeElement"></div>
                    </div>
                    <button type="submit" class="btn btn-success mt-3">Pay Now</button>
                </form>
            </div>
        </div>
    </div>
</div>




<script>
$(document).ready(function() {
    $('.complete-btn').on('click', function() {
        var orderId = $(this).data('id');
        var amount = $(this).data('amount');
        $("#payment_order_id").val(orderId);
        Swal.fire({
            title: 'Are you sure?',
            text: "You won't be able to revert this!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Yes, complete it!'
        }).then((result) => {
            if (result.isConfirmed) {
                if (amount === null) {
                    $("#paymentModal").modal("show");
                } else {
                    $.ajax({
                        url: '<?= base_url("business/complete_order"); ?>',
                        type: 'POST',
                        data: {
                            order_id: orderId
                        },
                        success: function(response) {
                            let data = JSON.parse(response)
                            if (data.success) {
                                Swal.fire('Completed!',
                                        'The order has been marked as complete.',
                                        'success')
                                    .then(() => location.reload());
                            } else {
                                Swal.fire('Error!', data.message, 'error');
                            }
                        }
                    });
                }
            }
        });
    });
});
</script>


<script src="https://js.stripe.com/v3/"></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script>
$(document).ready(function() {
      var stripe = Stripe(
        'pk_live_51QlU9YGj7vd04uS6VZDoWcCJ7GLQleVHrKfnHOF9yxup31WypzqdS0be1n3n5B6zMCUiGrJvy0gD8uFEbekfHEE000HRvEa6Ie'
    );
    var elements = stripe.elements();

    var style = {
        base: {
            color: '#32325d',
            fontFamily: '"Helvetica Neue", Helvetica, sans-serif',
            fontSmoothing: 'antialiased',
            fontSize: '16px',
            '::placeholder': {
                color: '#aab7c4'
            }
        },
        invalid: {
            color: '#fa755a',
            iconColor: '#fa755a'
        }
    };

    var cardNumber = elements.create('cardNumber', {
        style: style
    });
    var cardExpiry = elements.create('cardExpiry', {
        style: style
    });
    var cardCvc = elements.create('cardCvc', {
        style: style
    });

    cardNumber.mount('#card_number');
    cardExpiry.mount('#expiry_date');
    cardCvc.mount('#cvv');

    $("#payment-form").submit(function(event) {
        event.preventDefault();
        Swal.fire({
            title: "Processing Payment...",
            html: "Please wait while we process your payment.",
            allowOutsideClick: false,
            showConfirmButton: false,
            willOpen: () => {
                Swal.showLoading();
            }
        });

        stripe.createPaymentMethod({
            type: "card",
            card: cardNumber,
            billing_details: {
                name: `<?= $this->session->userdata('name')?>`,
                email: `<?= $this->session->userdata('email')?>`,
            }
        }).then(function(result) {
            if (result.error) {
                Swal.fire("Error!", result.error.message, "error");
            } else {
                $.ajax({
                    url: "<?= base_url('business/complete_result_based_order') ?>",
                    type: "POST",
                    data: {
                        order_id: $("#payment_order_id").val(),
                        amount: $("#payment_amount").val(),
                        payment_method_id: result.paymentMethod.id
                    },
                    dataType: "json",
                    success: function(response) {
                        if (response.requires_action) {
                            handleAuthentication(response
                                .payment_intent_client_secret, $("#payment_order_id").val(), $("#payment_amount").val());
                        } else if (response.success === true) {
                            Swal.fire("Success!",
                                    "Payment Transfered. Order has been Completed!", "success")
                                .then(() => {
                                    location.reload();
                                });
                        } else {
                            Swal.fire("Error!", response.message, "error");
                        }
                    }
                });
            }
        });
    });

    function handleAuthentication(clientSecret, order_id, amount) {
        stripe.confirmCardPayment(clientSecret).then(function(result) {
            if (result.error) {
                Swal.close();
                Swal.fire("Error!", result.error.message, "error");
            } else if (result.paymentIntent.status === 'succeeded') {
                $.ajax({
                    url: "<?= base_url('business/complete_result_based_order') ?>",
                    method: 'post',
                    data: {
                        order_id: order_id,
                        amount: amount,
                        payment_intent_id: result.paymentIntent.id
                    },
                    dataType: "json",
                    success: function(response) {
                        if (response.success) {
                            Swal.close();
                            Swal.fire("Success!",
                                "Payment Transfered. Order has been Completed!", "success")
                        }
                    }
                });
            } else {
                Swal.close();
                Swal.fire("Error!", "Payment failed. Please try again.", "error");
            }
        });
    }
});
</script>


<script>
$(document).ready(function() {
    $('.cancel-btn').on('click', function() {
        var orderId = $(this).data('id');
        Swal.fire({
            title: 'Are you sure?',
            text: "You won't be able to revert this!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Yes, cancel it!'
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: '<?= base_url("business/cancel_order"); ?>',
                    type: 'POST',
                    data: {
                        order_id: orderId
                    },
                    success: function(response) {
                        let data = JSON.parse(response)
                        if (data.success) {
                            Swal.fire('Completed!', 'The order has been cancelled.',
                                    'success')
                                .then(() => location.reload());
                        } else {
                            Swal.fire('Error!', data.message, 'error');
                        }
                    }
                });
            }
        });
    });
});
</script>


<script>
$(document).ready(function() {
    var orderId = 0;
    var spId = 0;

    // Open Review Modal
    $('.review-btn').on('click', function() {
        orderId = $(this).data('id');
        spId = $(this).data('sp');
        $('#rating-value').val(0);
        $('#review-text').val('');
        $('.star-rating .fa').removeClass('fa-star').addClass('fa-star-o');
        $('#reviewModal').modal('show');
    });

    // Star Rating Click Event
    $('.star-rating .fa').on('click', function() {
        var rating = $(this).data('rating');
        $('#rating-value').val(rating);

        $('.star-rating .fa').each(function() {
            if ($(this).data('rating') <= rating) {
                $(this).removeClass('fa-star-o').addClass('fa-star text-warning');
            } else {
                $(this).removeClass('fa-star text-warning').addClass('fa-star-o');
            }
        });
    });

    // Submit Review
    $('#submitReview').on('click', function() {
        var rating = $('#rating-value').val();
        var review = $('#review-text').val();

        if (rating == 0) {
            alert('Please select a star rating.');
            return;
        }

        $.ajax({
            url: '<?= base_url("Business/submit_review"); ?>',
            type: 'POST',
            data: {
                order_id: orderId,
                sp_id: spId,
                rating: rating,
                review: review
            },
            success: function(response) {
                let data = JSON.parse(response);
                if (data.status == 'success') {
                    $('#reviewModal').modal('hide');
                    Swal.fire({
                        icon: 'success',
                        title: 'Review Submitted',
                        text: 'Thank you for your feedback!'
                    }).then(function() {
                        location.reload(); // Refresh the page to hide the button
                    });
                } else {
                    alert('An error occurred. Please try again.');
                }
            }
        });
    });
});
</script>
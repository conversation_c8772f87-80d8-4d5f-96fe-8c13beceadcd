<main>
    <!-- Page Title -->
    <div class="page-title" data-aos="fade">
        <div class="heading">
            <div class="container">
                <div class="row d-flex justify-content-center text-center">
                    <div class="col-lg-8">
                        <h1>Profile</h1>
                        
                    </div>
                </div>
            </div>
        </div>
        <nav class="breadcrumbs">
            <div class="container">
                <ol>
                    <li><a href="<?=base_url()?>">Home</a></li>
                    <li class="current">Profile</li>
                </ol>
            </div>
        </nav>
    </div><!-- End Page Title -->

    <div class="section">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-8" data-aos="fade-up" data-aos-delay="200">
                    <form action="<?= site_url('Business/editProfile') ?>" enctype="multipart/form-data" method="post">
                        <?php
                    if($this->session->flashdata('update_error'))
                    {
                        echo '
                        <div class="alert alert-danger">
                        '.$this->session->flashdata("update_error").'
                        </div>
                        ';
                    }
                ?>
                        <?php
                    if($this->session->flashdata('update_success'))
                    {
                        echo '
                        <div class="alert alert-success">
                        '.$this->session->flashdata("update_success").'
                        </div>
                        ';
                    }
                ?>
                        <div class="row">
                            <div class="col-md-4 border-right">
                                <div class="d-flex flex-column align-items-center text-center p-3 py-5">
                                    <img id="profileImage" class="rounded-circle mt-5" width="150px"
                                        src="<?= base_url('assets/img/business/' . ($business->profile_picture ?? 'default.png')); ?>">
                                    <input type="file" id="imageUpload" name="profile_photo" class="d-none"
                                        accept="image/*">
                                    <button class="btn btn-outline-secondary mt-3" type="button"
                                        id="uploadButton">Upload Image</button>
                                </div>
                            </div>
                            <div class="col-md-8">
                                <div class="p-3 py-5">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h4 class="text-right">Profile Settings</h4>
                                    </div>

                                    <div class="row mt-2">
                                        <div class="col-md-12">
                                            <label class="labels">Business Name</label>
                                            <input type="text" class="form-control" name="business_name"
                                                placeholder="Business name" required
                                                value="<?= $business->business_name; ?>">
                                        </div>
                                    </div>
                                    <div class="row mt-3">
                                        <div class="col-md-12">
                                            <label class="labels">Industry</label>
                                            <select class="form-control" id="businessIndustry" name="industry" required>
                                                <option value="" disabled>Select Industry</option>
                                                <option value="Information Technology"
                                                    <?= $business->industry == 'Information Technology' ? 'selected' : ''; ?>>
                                                    Information Technology</option>
                                                <option value="Healthcare"
                                                    <?= $business->industry == 'Healthcare' ? 'selected' : ''; ?>>
                                                    Healthcare</option>
                                                <option value="Finance"
                                                    <?= $business->industry == 'Finance' ? 'selected' : ''; ?>>Finance
                                                </option>
                                                <option value="Education"
                                                    <?= $business->industry == 'Education' ? 'selected' : ''; ?>>
                                                    Education</option>
                                                <option value="Manufacturing"
                                                    <?= $business->industry == 'Manufacturing' ? 'selected' : ''; ?>>
                                                    Manufacturing</option>
                                                <option value="Retail"
                                                    <?= $business->industry == 'Retail' ? 'selected' : ''; ?>>Retail
                                                </option>
                                                <option value="Construction"
                                                    <?= $business->industry == 'Construction' ? 'selected' : ''; ?>>
                                                    Construction</option>
                                                <option value="Transportation"
                                                    <?= $business->industry == 'Transportation' ? 'selected' : ''; ?>>
                                                    Transportation</option>
                                                <option value="Entertainment"
                                                    <?= $business->industry == 'Entertainment' ? 'selected' : ''; ?>>
                                                    Entertainment</option>
                                                <option value="Other"
                                                    <?= $business->industry == 'Other' ? 'selected' : ''; ?>>Other
                                                </option>
                                            </select>
                                        </div>
                                        <div class="col-md-12 mt-3">
                                            <label for="businessEmail" class="form-label">Email</label>
                                            <input type="email" class="form-control" id="businessEmail" name="email"
                                                placeholder="Email" readonly value="<?= $business->email; ?>">
                                        </div>
                                        <div class="col-md-12 mt-3">
                                            <label for="kvk-vat" class="form-label">KVK/VAT Number</label>
                                            <input type="text" class="form-control" id="kvk-vat"
                                                name="kvk_vat" value="<?= $business->kvk_vat; ?>" required>
                                        </div>
                                        <div class="col-md-12 mt-3">
                                            <label class="labels">Goals</label>
                                            <textarea name="goals" placeholder="Goals"
                                                class="form-control"><?= $business->goals; ?></textarea>
                                        </div>
                                    </div>
                                    <div class="mt-5 text-center">
                                        <button class="btn btn-success w-100 d-block" type="submit">Save
                                            Profile</button>
                                    </div>

                                </div>
                            </div>
                        </div>

                    </form>
                </div>
            </div>
        </div>
    </div>
</main>

<script>
$(document).ready(function() {
    $('#uploadButton').click(function() {
        $('#imageUpload').click();
    });

    $('#imageUpload').change(function() {
        const file = this.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(event) {
                $('#profileImage').attr('src', event.target.result);
            };
            reader.readAsDataURL(file);
        }
    });
});
</script>
<main class="main">

    <!-- Page Title -->
    <div class="page-title" data-aos="fade">
        <div class="heading">
            <div class="container">
                <div class="row d-flex justify-content-center text-center">
                    <div class="col-lg-8">
                        <h1>Inbox<br></h1>
                        <p class="mb-0 mt-3">
                        </p>
                    </div>
                </div>
            </div>
        </div>
        <nav class="breadcrumbs">
            <div class="container">
                <ol>
                    <li><a href="<?= base_url() ?>">Home</a></li>
                    <li class="current">Inbox<br></li>
                </ol>
            </div>
        </nav>
    </div><!-- End Page Title -->
    <div class="container p-0 my-5">

        <h3 class="text-center mb-4">Messages</h3>

        <div class="card bg-white">
            <div class="row g-0">
                <div class="col-12 col-lg-5 col-xl-3 border-end sidebar">

                    <div class="px-4 d-none d-md-block">
                        <div class="d-flex align-items-center">
                            <div class="flex-grow-1">
                                <input type="text" class="form-control my-3" placeholder="Search...">
                            </div>
                        </div>
                    </div>

                    <?php
                    foreach($chats as $chat){
                    ?>
                    <a data-receiver-id='<?=$this->session->userdata('b_id')? $chat['freelancer_id']: $chat['business_id']?>'
                        data-id='<?=$chat['chat_id']?>'
                        class="list-group-item list-group-item-action border-0 p-3 mb-3 <?=$this->uri->segment(2) == $chat['chat_id'] ? "active-chat":""?>">
                        <span class="badge bg-danger rounded-pill unread-badge d-none float-end"></span>
                        <div class="d-flex align-items-start">
                            <?php
                            if($this->session->userdata('s_id')){
                            ?>
                            <img src="<?= base_url('assets/img/business/' . ($chat['profile_picture'] ?? 'default.png')); ?>"
                                class="rounded-circle me-1" alt="<?=$chat['business_name']?>" width="40" height="40">
                            <?php
                            }
                            else{
                                ?>
                            <img src="<?= base_url('assets/img/sales/' . ($chat['profile_picture'] ?? 'default.png')); ?>"
                                class="rounded-circle me-1" alt="<?=$chat['full_name']?>" width="40" height="40">
                            <?php
                            }
                            ?>
                            <div class="flex-grow-1 ms-3">
                                <b><?=$this->session->userdata('s_id')?$chat['business_name']:$chat['full_name']?></b>

                            </div>
                        </div>
                    </a>
                    <?php
                    }
                    ?>


                    <hr class="d-block d-lg-none mt-1 mb-0">
                </div>
                <div class="col-12 col-lg-7 col-xl-9 messages">



                    <div class='chat-container <?=$this->uri->segment(2) ? "":"d-none"?>'>
                        <div class="py-2 px-4 border-bottom chat-header">

                        </div>

                        <div class="position-relative">
                            <div class="chat-messages p-4" id="chat-messages">


                            </div>
                        </div>

                        <div class="flex-grow-0 py-3 px-4 border-top">
                            <div class="input-group">
                                <?php
                             if($this->session->userdata('s_id')){
                             ?>
                                <button class="btn btn-dark create-offer">Create Offer</button>
                                <?php
                             }
                            ?>
                                <input type="text" class="form-control message-input" placeholder="Type your message">
                                <button class="btn btn-success btn-send">Send</button>
                            </div>
                        </div>
                    </div>



                    <div class='no-chat text-center py-5 <?=$this->uri->segment(2) ? "d-none":""?>'>
                        <img src="<?=base_url()?>assets/img/no-selection.webp" alt="" width='219'>
                        <h4 class='text-dark text-center m-0 my-3'>Pick up where you left off</h4>
                        <h6 class='text-muted text-center'>Select a conversation and chat away.</h6>
                    </div>


                </div>
            </div>
        </div>
    </div>
</main>


<!-- Offer Modal -->
<div class="modal fade" id="offerModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Send a Custom Offer</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <input type="text" id="offer-title" class="form-control mb-2" placeholder="Offer Title">
                <textarea id="offer-desc" class="form-control mb-2" placeholder="Offer Description"></textarea>
                <select id="payment-type" class="form-control mb-2">
                    <option value="hourly">Hourly</option>
                    <option value="hourly_bonus">Hourly + Bonus</option>
                    <option value="result_based">Result-Based</option>
                </select>

                <!-- Hourly Fields -->
                <div id="hourly-fields" class="payment-fields">
                    <input type="number" id="hourly-rate" class="form-control mb-2" placeholder="Hourly Rate ($)">
                </div>

                <!-- Bonus Field -->
                <div id="bonus-field" class="payment-fields">
                    <input type="number" id="bonus" class="form-control mb-2" placeholder="Bonus ($)">
                </div>

                <!-- Result-Based Fields -->
                <div id="result-based-fields" class="payment-fields">
                    <textarea id="result-criteria" class="form-control mb-2" placeholder="Result Criteria"></textarea>
                </div>

                <input type="number" id="offer-revisions" class="form-control mb-2" placeholder="Revisions">
                <input type="number" id="offer-delivery" class="form-control mb-2" placeholder="Delivery Time (days)">
            </div>
            <div class="modal-footer">
                <button class="btn btn-primary" id="send-offer">Send Offer</button>
            </div>
        </div>
    </div>
</div>



<div class="modal fade" id="paymentModal" tabindex="-1" aria-labelledby="paymentModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="paymentModalLabel">Enter Payment Details</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="payment-form">
                    <input type="hidden" id="bid_id">
                    <input type="hidden" id="user_id">
                    <input type="hidden" id="bid_amount">
                    <div class="mb-3">
                        <label for="card_number" class="form-label">Card Number *</label>
                        <div id="card_number" class="StripeElement"></div>
                    </div>
                    <div class="mb-3">
                        <label for="expiry_date" class="form-label">Expiry Date *</label>
                        <div id="expiry_date" class="StripeElement"></div>
                    </div>
                    <div class="mb-3">
                        <label for="cvv" class="form-label">CVC *</label>
                        <div id="cvv" class="StripeElement"></div>
                    </div>
                    <button type="submit" class="btn btn-primary mt-3">Pay Now</button>
                </form>
            </div>
        </div>
    </div>
</div>


<script>
$(document).ready(function() {
    var chatIdFromUrl = '<?= $this->uri->segment(2); ?>';

    if (chatIdFromUrl) {
        getChatMessages(chatIdFromUrl);
    }

    $('.list-group-item').click(function(e) {
        e.preventDefault();
        var chatId = $(this).data('id');
        $(".no-chat").addClass("d-none");
        $(".chat-container").removeClass('d-none');
        $('.list-group-item').removeClass("active-chat");
        $(this).addClass("active-chat")
        getChatMessages(chatId);
    });

    $(".btn-send").click(function() {
        sendMessage();
    });

    $(".message-input").keypress(function(event) {
        if (event.which == 13) {
            sendMessage();
        }
    });


    setInterval(function() {
        var chatId = '<?= $this->uri->segment(2); ?>';
        if (chatId) {
            getChatMessages(chatId);
        }
    }, 5000);

});

// Function to load chat messages
function getChatMessages(chatId) {
    $.ajax({
        url: '<?= base_url() ?>chat/getChatMessages',
        method: 'POST',
        data: {
            chat_id: chatId
        },
        success: function(response) {
            var result = JSON.parse(response);
            let messages = result.messages;
            let chatDetails = result.chatDetails;
            let messagesHtml = '';
            let chatHtml = '';
            if (<?= $this->session->userdata('b_id') ? 'true' : 'false'; ?>) {
                chatHtml += `<div class="d-flex align-items-center py-1">
                                    <div class="position-relative">
                                        <img src="<?=base_url()?>assets/img/sales/${chatDetails.sImage}"
                                            class="rounded-circle me-1" alt="${chatDetails.full_name}" width="40" height="40">
                                    </div>
                                    <div class="flex-grow-1 pl-3">
                                        <strong>${chatDetails.full_name}</strong>
                                    </div>
                                    
                                </div>`;
            } else {
                chatHtml += `<div class="d-flex align-items-center py-1">
                                    <div class="position-relative">
                                        <img src="<?=base_url()?>assets/img/business/${chatDetails.bImage}"
                                            class="rounded-circle me-1" alt="${chatDetails.business_name}" width="40" height="40">
                                    </div>
                                    <div class="flex-grow-1 pl-3">
                                        <strong>${chatDetails.business_name}</strong>
                                    </div>
                                    
                                </div>`;
            }

            $('.chat-header').html(chatHtml);

            messages.forEach(function(message) {
                var messageHtml = '';
                // Format the created_at timestamp to 10:55 PM format
                var date = new Date(message
                    .messageTime); // Assuming `created_at` is in MySQL timestamp format
                var time = date.toLocaleTimeString([], {
                    hour: '2-digit',
                    minute: '2-digit',
                    hour12: true
                });

                if (<?= $this->session->userdata('b_id') ? 'true' : 'false'; ?>) {
                    if (message.sender_id === message.business_id && message.sender_id ===
                        `<?=$this->session->userdata('b_id')?>`) {
                        messageHtml = `
                            <div class="chat-message-right pb-4">
                                <div>
                                    <img src="<?=base_url()?>assets/img/business/${message.bImage}"
                                        class="rounded-circle me-1" alt="${message.business_name}" width="40" height="40">
                                    <div class="text-muted small text-nowrap mt-2">${time}</div>
                                </div>
                                <div class="flex-shrink-1 bg-light rounded py-2 px-3 me-3">
                                    ${message.message}
                                </div>
                            </div>
                        `;
                    } else {
                        messageHtml = `
                            <div class="chat-message-left pb-4">
                                <div>
                                    <img src="<?=base_url()?>assets/img/sales/${message.sImage}"
                                        class="rounded-circle me-1" alt="${message.full_name}" width="40" height="40">
                                    <div class="text-muted small text-nowrap mt-2">${time}</div>
                                </div>
                                <div class="flex-shrink-1 bg-light rounded py-2 px-3 ms-3">
                                    <div class="font-weight-bold mb-1"></div>
                                    ${message.message}
                                </div>
                            </div>
                        `;
                    }
                }

                if (<?= $this->session->userdata('s_id') ? 'true' : 'false'; ?>) {
                    if (message.sender_id === message.freelancer_id && message.sender_id ===
                        `<?=$this->session->userdata('s_id')?>`) {
                        messageHtml = `
                            <div class="chat-message-right pb-4">
                                <div>
                                    <img src="<?=base_url()?>assets/img/sales/${message.sImage}"
                                        class="rounded-circle me-1" alt="${message.full_name}" width="40" height="40">
                                    <div class="text-muted small text-nowrap mt-2">${time}</div>
                                </div>
                                <div class="flex-shrink-1 bg-light rounded py-2 px-3 me-3">
                                    ${message.message}
                                </div>
                            </div>
                        `;
                    } else {
                        messageHtml = `
                            <div class="chat-message-left pb-4">
                                <div>
                                    <img src="<?=base_url()?>assets/img/business/${message.bImage}"
                                        class="rounded-circle me-1" alt="${message.business_name}" width="40" height="40">
                                    <div class="text-muted small text-nowrap mt-2">${time}</div>
                                </div>
                                <div class="flex-shrink-1 bg-light rounded py-2 px-3 ms-3">
                                    <div class="font-weight-bold mb-1"></div>
                                    ${message.message}
                                </div>
                            </div>
                        `;
                    }
                }

                // Append each message to the messages container
                messagesHtml += messageHtml;
            });

            // Insert the generated HTML into the chat-messages div
            $('#chat-messages').html(messagesHtml);
            markMessagesAsRead(chatId);
            // Update the URL to include the chat ID (without query string)
            var newUrl = '<?=base_url()?>inbox/' + chatId;
            history.pushState(null, null, newUrl);
        },
        error: function() {
            alert('An error occurred while fetching the chat messages.');
        }
    });
}

function markMessagesAsRead(chatId) {
    $.ajax({
        url: '<?= base_url() ?>chat/markAsRead',
        method: 'POST',
        data: {
            chat_id: chatId
        }
    });
}

function fetchUnreadCounts() {
    $.ajax({
        url: '<?= base_url() ?>chat/getUnreadCounts',
        method: 'GET',
        success: function(response) {
            var unreadCounts = JSON.parse(response);
            $('.list-group-item').each(function() {
                var chatId = $(this).data('id');
                var unreadCount = unreadCounts.find(item => item.chat_id == chatId)?.unread_count ||
                    0;

                if (unreadCount > 0) {
                    $(this).find('.unread-badge').text(unreadCount).removeClass('d-none');
                } else {
                    $(this).find('.unread-badge').addClass('d-none');
                }
            });
        }
    });
}

// Fetch unread counts every 5 seconds
setInterval(fetchUnreadCounts, 5000);



function sendMessage() {
    var chatId = $(".active-chat").data('id');
    var message = $(".message-input").val().trim();

    if (message === "") {
        return;
    }

    if (containsRestrictedContent(message)) {
        Swal.fire({
            title: "Warning!",
            text: "Sharing contact details is not allowed!",
            icon: "warning"
        });
        return false;
    }

    $.ajax({
        url: '<?= base_url() ?>chat/sendMessage',
        method: 'POST',
        data: {
            chat_id: chatId,
            message: message,
            receiver_id: $(".active-chat").data('receiver-id')
        },
        success: function(response) {
            $(".message-input").val('');
            getChatMessages(chatId); // Reload messages after sending
        },
        error: function() {
            alert('Failed to send message.');
        }
    });
}

function containsRestrictedContent(message) {
    var blockedPatterns = [
        /\b\d{10,15}\b/, // Phone numbers (10 to 15 digits)
        /\b(?:facebook|fb|telegram|whatsapp|twitter|snapchat|instagram|linkedin|skype)\b/i, // Social media
        /(?:@|at)\s?(?:gmail|yahoo|hotmail|outlook|protonmail|aol)\.com/i, // Emails
        /\b(?:\+\d{1,3}[-.\s]?)?\(?\d{2,4}\)?[-.\s]?\d{3,5}[-.\s]?\d{3,5}\b/ // Phone formats with +, -, ()
    ];

    var allowedPatterns = [
        /zoom\.us\/j\//i, // Zoom Links
        /meet\.google\.com/i // Google Meet Links
    ];

    for (var i = 0; i < allowedPatterns.length; i++) {
        if (allowedPatterns[i].test(message)) {
            return false; // Allowed content, no blocking
        }
    }

    for (var i = 0; i < blockedPatterns.length; i++) {
        if (blockedPatterns[i].test(message)) {
            return true; // Block content
        }
    }

    return false;
}
</script>




<script>
$(document).ready(function() {
    // Show modal when "Create Offer" button is clicked
    $(".create-offer").click(function() {
        $("#offerModal").modal("show");
    });

    $("#payment-type").change(function() {
        const type = $(this).val();
        $(".payment-fields").hide();
        if (type === "hourly") {
            $("#hourly-fields").show();
        } else if (type === "hourly_bonus") {
            $("#hourly-fields").show();
            $("#bonus-field").show();
        } else if (type === "result_based") {
            $("#result-based-fields").show();
        }
    }).trigger("change");

    // Send Offer via AJAX
    $("#send-offer").click(function() {
        var chatId = $(".active-chat").data("id"); // Get active chat ID
        var senderId = "<?php echo $this->session->userdata('s_id'); ?>"; // Freelancer's ID
        var receiverId = $(".active-chat").data("receiver-id"); // Get receiver ID (business user)

        var offerData = {
            chat_id: chatId,
            sender_id: senderId,
            receiver_id: receiverId,
            title: $("#offer-title").val(),
            description: $("#offer-desc").val(),
            payment_type: $("#payment-type").val(),
            hourly_rate: $("#hourly-rate").val(),
            bonus: $("#bonus").val(),
            result_criteria: $("#result-criteria").val(),
            revisions: $("#offer-revisions").val(),
            delivery_time: $("#offer-delivery").val()
        };

        $.ajax({
            url: "<?php echo base_url('Offer/create_offer'); ?>",
            type: "POST",
            data: offerData,
            dataType: "json",
            success: function(response) {
                if (response.status) {
                    let paymentDetails = '';
                    if (offerData.payment_type === 'hourly') {
                        paymentDetails =
                            `<strong>Hourly Rate:</strong> $${offerData.hourly_rate}/hr`;
                    } else if (offerData.payment_type === 'hourly_bonus') {
                        paymentDetails =
                            `<strong>Hourly Rate:</strong> $${offerData.hourly_rate}/hr + <strong>Bonus:</strong> $${offerData.bonus}`;
                    } else {
                        paymentDetails =
                            `<strong>Result-Based Criteria:</strong> ${offerData.result_based}`;
                    }

                    var offerMessage = `
                        <div class="offer-message">
                            <h5>${offerData.title}</h5>
                            <p>${offerData.description}</p>
                            <p><strong>Payment Type:</strong> ${offerData.payment_type}</p>
                            <p>${paymentDetails}</p>
                            <p><strong>Revisions:</strong> ${offerData.revisions}</p>
                            <p><strong>Delivery Time:</strong> ${offerData.delivery_time} days</p>
                            <button class="btn btn-success accept-offer" 
                            data-amount="${offerData.hourly_rate}" 
                            data-sender-id="${offerData.sender_id}" 
                            data-offer-id="${response.offer_id}" 
                            data-payment-type="${offerData.payment_type}">
                            Accept Offer
                            </button>
                        </div>
                        `;


                    // Save offer message in messages table via AJAX
                    $.ajax({
                        url: "<?php echo base_url('chat/sendMessage'); ?>",
                        type: "POST",
                        data: {
                            chat_id: chatId,
                            sender_id: senderId,
                            receiver_id: receiverId,
                            message: offerMessage,
                            is_offer: 1,
                            offer_id: response.offer_id
                        },
                        success: function(msgResponse) {
                            getChatMessages(chatId);
                            $("#offerModal").modal("hide");

                        }
                    });
                } else {
                    alert(response.message);
                }
            }
        });
    });

});
</script>

<script src="https://js.stripe.com/v3/"></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>



<script>
$(document).ready(function() {

    var stripe = Stripe(
        'pk_live_51QlU9YGj7vd04uS6VZDoWcCJ7GLQleVHrKfnHOF9yxup31WypzqdS0be1n3n5B6zMCUiGrJvy0gD8uFEbekfHEE000HRvEa6Ie'
    );
    var elements = stripe.elements();

    var style = {
        base: {
            color: '#32325d',
            fontFamily: '"Helvetica Neue", Helvetica, sans-serif',
            fontSmoothing: 'antialiased',
            fontSize: '16px',
            '::placeholder': {
                color: '#aab7c4'
            }
        },
        invalid: {
            color: '#fa755a',
            iconColor: '#fa755a'
        }
    };

    var cardNumber = elements.create('cardNumber', {
        style: style
    });
    var cardExpiry = elements.create('cardExpiry', {
        style: style
    });
    var cardCvc = elements.create('cardCvc', {
        style: style
    });

    cardNumber.mount('#card_number');
    cardExpiry.mount('#expiry_date');
    cardCvc.mount('#cvv');

    $(document).on("click", ".accept-offer", function() {
        var offerId = $(this).data("offer-id");
        var paymentType = $(this).data("payment-type");
        var userId = $(this).data("sender-id");

        if (paymentType === "result_based") {
            // Directly accept the offer without payment
            $.ajax({
                url: "<?php echo base_url('Offer/accept_offer'); ?>",
                type: "POST",
                data: {
                    offer_id: offerId,
                    user_id: userId
                },
                success: function(response) {
                    if (response.status) {
                        alert("Offer Accepted!");
                    } else {
                        alert("Failed to accept offer.");
                    }
                }
            });
        } else {
            // Proceed with payment flow
            let userId = $(this).data("sender-id");
            let amount = $(this).data("amount");

            Swal.fire({
                title: "Are you sure?",
                text: "You are about to accept this offer and proceed to payment.",
                icon: "warning",
                showCancelButton: true,
                confirmButtonText: "Yes, Pay Now!",
                cancelButtonText: "Cancel"
            }).then((result) => {
                if (result.isConfirmed) {
                    $("#bid_id").val(offerId);
                    $("#user_id").val(userId);
                    $("#bid_amount").val(amount);
                    $("#paymentModal").modal("show");
                }
            });
        }
    });

    $("#payment-form").submit(function(event) {
        event.preventDefault();
        Swal.fire({
            title: "Processing Payment...",
            html: "Please wait while we process your payment.",
            allowOutsideClick: false,
            showConfirmButton: false,
            willOpen: () => {
                Swal.showLoading();
            }
        });

        stripe.createPaymentMethod({
            type: "card",
            card: cardNumber,
            billing_details: {
                name: `<?= $this->session->userdata('name')?>`,
                email: `<?= $this->session->userdata('email')?>`,
            }
        }).then(function(result) {
            if (result.error) {
                Swal.fire("Error!", result.error.message, "error");
            } else {
                $.ajax({
                    url: "<?= base_url('offer/process_payment') ?>",
                    type: "POST",
                    data: {
                        offer_id: $("#bid_id").val(),
                        user_id: $("#user_id").val(),
                        amount: $("#bid_amount").val(),
                        payment_method_id: result.paymentMethod.id
                    },
                    dataType: "json",
                    success: function(response) {
                        if (response.requires_action) {
                            handleAuthentication(response
                                .payment_intent_client_secret);
                        } else if (response.status === true) {
                            Swal.fire("Success!",
                                    "Payment Successful. Order Placed!", "success")
                                .then(() => {
                                    location.reload();
                                });
                        } else {
                            Swal.fire("Error!", response.message, "error");
                        }
                    }
                });
            }
        });
    });

    function handleAuthentication(clientSecret) {
        stripe.confirmCardPayment(clientSecret).then(function(result) {
            if (result.error) {
                Swal.close();
                Swal.fire("Error!", result.error.message, "error");
            } else if (result.paymentIntent.status === 'succeeded') {
                $.ajax({
                    url: "<?= base_url('offer/confirm_payment') ?>",
                    method: 'post',
                    data: {
                        payment_intent_id: result.paymentIntent.id
                    },
                    dataType: "json",
                    success: function(response) {
                        if (response.success) {
                            Swal.close();
                            Swal.fire("Success!",
                                "Payment Successful. Order Placed!", "success")
                        }
                    }
                });
            } else {
                Swal.close();
                Swal.fire("Error!", Payment failed.Please
                    try again., "error");
            }
        });
    }
});
</script>
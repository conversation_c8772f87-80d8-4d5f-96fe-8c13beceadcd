<div class="hero page-inner overlay" style="background-image: url('<?=base_url()?>assets/images/checkout-bg.jpg')">
    <div class="container">
        <div class="row justify-content-center align-items-center">
            <div class="col-lg-9 text-center mt-5">
                <h1 class="heading" data-aos="fade-up">Checkout</h1>

                <nav aria-label="breadcrumb" data-aos="fade-up" data-aos-delay="200">
                    <ol class="breadcrumb text-center justify-content-center">
                        <li class="breadcrumb-item"><a href="<?=base_url()?>">Home</a></li>
                        <li class="breadcrumb-item active text-white-50" aria-current="page">
                            Checkout
                        </li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>
</div>


<section class="section">
    <div class="container checkout-container">
        <div class="row">
            <!-- Payment Methods -->
            <div class="col-lg-6 col-md-12">
                <h3>Payment Method</h3>
                <div class="payment-method">
                    <!-- Radio Buttons for Payment Method -->
                    <div class="mb-3 payment-option">
                        <input type="radio" id="credit_card" name="payment_method" value="credit_card" checked>
                        <img src="https://i.imgur.com/sB4jftM.png" alt="Credit Card">
                    </div>
                    <div class="mb-3 payment-option">
                        <input type="radio" id="paypal" name="payment_method" value="paypal">
                        <img src="https://i.imgur.com/yK7EDD1.png" alt="PayPal">
                    </div>
                    <div class="mb-3 payment-option">
                        <input type="radio" id="direct_bank_transfer" name="payment_method" value="direct_bank_transfer">
                        <img src="<?=base_url()?>assets/images/bank-transfer.png" alt="PayPal">
                    </div>

                    <!-- PayPal Form -->
                    <div id="paypal_form" class="form-section">
                        <div id="paypal-button-container"></div>
                    </div>

                    <!-- Credit Card Form -->
                    <div id="credit_card_form" class="form-section active">
                        <form>
                            <div class="mb-3">
                                <label for="card_number" class="form-label">Card Number *</label>
                                <div id="card_number" class="StripeElement"></div>
                            </div>
                            <div class="mb-3">
                                <label for="expiry_date" class="form-label">Expiry Date *</label>
                                <div id="expiry_date" class="StripeElement"></div>
                            </div>
                            <div class="mb-3">
                                <label for="cvv" class="form-label">CVC *</label>
                                <div id="cvv" class="StripeElement"></div>
                            </div>
                            <input type="checkbox" name="term_agree" value="Agree"> Agree on Terms & Conditions
                            <button type="button" class="btn btn-success btn-pay mt-3">Pay with Credit Card</button>
                        </form>
                    </div>

                    <!-- Direct Bank Transfer -->
                    <div id="bank-transfer" class="form-section">
                        <input type="checkbox" name="term_agree" value="Agree"> Agree on Terms & Conditions
                        <button type="button" class="btn btn-success d-block mt-3 w-100">Direct Bank Transfer</button>
                    </div>
                </div>
            </div>

            <!-- Subscription Details -->
            <div class="col-lg-6 col-md-12">
                <h3>Subscription Details</h3>
                <div class="subscription-details">
                    <p><strong>Package Name:</strong> Premium</p>
                    <p><strong>Total Amount:</strong> $29 / Monthly</p>
                    <p><strong>Features:</strong></p>
                    <ul>
                        <li>15 Websites</li>
                        <li>50GB Disk Space</li>
                        <li>50 Email Accounts</li>
                        <li>Unlimited Support</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    <!--- END CONTAINER -->
</section>

<script src="https://js.stripe.com/v3/"></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
$(document).ready(function() {
    // Show the corresponding form based on the selected payment method
    $('input[name="payment_method"]').change(function() {
        if ($(this).val() === 'paypal') {
            $('#paypal_form').show();
            $('#credit_card_form').hide();
            $('#bank-transfer').hide();
        } else if($(this).val() === 'credit_card') {
            $('#paypal_form').hide();
            $('#credit_card_form').show();
            $('#bank-transfer').hide();
        }
        else{
            $('#paypal_form').hide();
            $('#credit_card_form').hide();
            $('#bank-transfer').show();
        }
    });
});
</script>

<script>
$(document).ready(function() {
    var stripe = Stripe(
        'pk_test_51QcxY4BxQ1TXkwrLCdQOtN72SmUXbT7EydJsmlxkvQ4UUUgyKsJBRy2mN5peAXCo69V5MLtrFrniyazcx3HMgoJN00UGZkg36c'
    );
    var elements = stripe.elements();

    var style = {
        base: {
            color: '#32325d',
            fontFamily: '"Helvetica Neue", Helvetica, sans-serif',
            fontSmoothing: 'antialiased',
            fontSize: '16px',
            '::placeholder': {
                color: '#aab7c4'
            }
        },
        invalid: {
            color: '#fa755a',
            iconColor: '#fa755a'
        }
    };

    // Create Card elements
    var cardNumber = elements.create('cardNumber', {
        style: style
    });
    var cardExpiry = elements.create('cardExpiry', {
        style: style
    });
    var cardCvc = elements.create('cardCvc', {
        style: style
    });

    cardNumber.mount('#card_number');
    cardExpiry.mount('#expiry_date');
    cardCvc.mount('#cvv');


    $('.btn-pay').click(function() {
        var selectedPaymentMethod = $('input[name="payment_method"]:checked').val();
        if (selectedPaymentMethod === 'credit_card') {
            Swal.fire({
                title: 'Processing Payment...',
                text: 'Please wait while we process your payment.',
                icon: 'info',
                allowOutsideClick: false,
                showCancelButton: false,
                showConfirmButton: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });
            createPaymentMethod();
        }
    });

    function createPaymentMethod() {
        stripe.createPaymentMethod({
            type: 'card',
            card: cardNumber,
            billing_details: {
                name: `<?= $this->session->userdata('first_name') . " " . $this->session->userdata('last_name') ?>`,
                email: `<?= $this->session->userdata('email')?>`,
            }
        }).then(function(result) {
            if (result.error) {
                Swal.close(); // Close SweetAlert
                showError(result.error.message); // Show error message
            } else {
                processPayment(result.paymentMethod.id);
            }
        });
    }

    function processPayment(paymentMethodId) {
        var postData = {
            payment_method_id: paymentMethodId,
            customer_name: `<?= $this->session->userdata('first_name') . " " . $this->session->userdata('last_name') ?>`,
            email: `<?= $this->session->userdata('email')?>`,
            plan_id: `<?=$this->input->get('plan')?>`
        };

        $.post('<?= base_url("payment/process_payment") ?>', postData).done(function(response) {
            var data = JSON.parse(response);
            if (data.requires_action) {
                handleAuthentication(data.payment_intent_client_secret);
            } else if (data.success) {
                Swal.close(); // Close SweetAlert
                showSuccess('Payment successful!');
            } else {
                Swal.close(); // Close SweetAlert
                showError('Payment failed. Please try again.');
            }
        }).fail(function() {
            Swal.close(); // Close SweetAlert
            showError('An error occurred. Please try again.');
        });
    }

    function handleAuthentication(clientSecret) {
        stripe.confirmCardPayment(clientSecret).then(function(result) {
            if (result.error) {
                Swal.close(); // Close SweetAlert
                showError(result.error.message);
            } else if (result.paymentIntent.status === 'succeeded') {
                $.ajax({
                    url: "<?= base_url('payment/confirm_payment') ?>",
                    method: 'post',
                    data: {
                        payment_intent_id: result.paymentIntent.id
                    },
                    dataType: "json",
                    success: function(response) {
                        if (response.success) {
                            Swal.close(); // Close SweetAlert
                            showSuccess('Payment successful!');
                        }
                    }
                });
            } else {
                Swal.close(); // Close SweetAlert
                showError('Payment failed. Please try again.');
            }
        });
    }


    paypal.Buttons({
        createOrder: function(data, actions) {
            return actions.order.create({
                purchase_units: [{
                    amount: {
                        value: '29.00'
                    }
                }]
            });
        },
        onApprove: function(data, actions) {
            return actions.order.capture().then(function(details) {
                Swal.fire({
                    title: 'Processing Payment...',
                    text: 'Please wait while we process your payment.',
                    icon: 'info',
                    allowOutsideClick: false,
                    showCancelButton: false,
                    showConfirmButton: false,
                    didOpen: () => {
                        Swal.showLoading();
                    }
                });

                $.post('<?= base_url('payment/process_paypal_payment') ?>', {
                    orderId: data.orderID,
                    payerId: data.payerID
                }).done(function(response) {
                    var result = JSON.parse(response);
                    Swal.close();
                    if (result.success) {
                        Swal.fire('Success', 'Payment successful!', 'success');
                    } else {
                        Swal.fire('Error', 'Payment failed. Please try again.',
                            'error');
                    }
                }).fail(function() {
                    Swal.close();
                    Swal.fire('Error', 'An error occurred. Please try again.',
                        'error');
                });
            });
        }
    }).render('#paypal-button-container');

    function showSuccess(message) {
        Swal.fire({
            icon: 'success',
            title: 'Success',
            text: message,
            confirmButtonText: 'OK'
        }).then((result) => {
            if (result.isConfirmed) {
                window.location.href = '<?= base_url('manage-subscription') ?>';
            }
        });
    }

    function showError(message) {
        Swal.fire({
            icon: 'error',
            title: 'Error',
            text: message,
            confirmButtonText: 'OK'
        });
    }
});
</script>
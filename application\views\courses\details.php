<main class="main">
    <!-- Page Title -->
    <div class="page-title" data-aos="fade">
        <div class="heading">
            <div class="container">
                <div class="row d-flex justify-content-center text-center">
                    <div class="col-lg-8">
                        <h1><?= htmlspecialchars($course->title); ?></h1>
                    </div>
                </div>
            </div>
        </div>
        <nav class="breadcrumbs">
            <div class="container">
                <ol>
                    <li><a href="<?= base_url(); ?>">Home</a></li>
                    <li class="current"><?= htmlspecialchars($course->title); ?></li>
                </ol>
            </div>
        </nav>
    </div>
    <!-- End Page Title -->

    <section id="courses-course-details" class="courses-course-details section">
        <div class="container" data-aos="fade-up">
            <div class="row">
                <!-- Left Content -->
                <div class="col-lg-8">
                    <?php if (!empty($course->intro_video)): ?>
                    <div class="embed-responsive embed-responsive-16by9 mb-3">
                        <iframe class="embed-responsive-item" src="<?= htmlspecialchars($course->intro_video); ?>"
                            allowfullscreen></iframe>
                    </div>
                    <?php else: ?>
                    <img src="<?= base_url('assets/course_thumbnail/' . $course->thumbnail); ?>" class="img-fluid mb-3"
                        alt="<?= htmlspecialchars($course->title); ?>">
                    <?php endif; ?>

                    <h3 class="mb-3"><?= htmlspecialchars($course->title); ?></h3>

                    <p><?= nl2br(htmlspecialchars($course->description)); ?></p>
                </div>

                <!-- Right Sidebar -->
                <div class="col-lg-4">
                    <div class="course-info d-flex justify-content-between align-items-center">
                        <h5>Level</h5>
                        <p><?= htmlspecialchars($course->level); ?></p>
                    </div>

                    <div class="course-info d-flex justify-content-between align-items-center">
                        <h5>Language</h5>
                        <p><?= htmlspecialchars($course->language); ?></p>
                    </div>

                    <div class="course-info d-flex justify-content-between align-items-center">
                        <h5>Price</h5>
                        <p><?= $course->price > 0 ? '$' . $course->price : 'Free'; ?></p>
                    </div>

                    <div class="course-info d-flex justify-content-between align-items-center">
                        <h5>Created</h5>
                        <p><?= date('F j, Y', strtotime($course->created_at)); ?></p>
                    </div>
                    <?php if($this->session->userdata('s_id') || $this->session->userdata('b_id')): ?>
                    <div class="mt-2">
                        <?php if(empty($course->price) || $course->price <= 0): ?>
                        <a href="<?= base_url('course/enroll/' . $course->id); ?>"
                            class="btn btn-success d-block w-100">
                            <i class="fa fa-check-circle"></i> Enroll Now
                        </a>
                        <?php else: ?>
                        <button type="button" class="btn btn-success d-block w-100"
                            onclick="openCoursePaymentModal(<?= $course->id ?>, <?= $course->price ?>)">
                            <i class="fa fa-credit-card"></i> Enroll (<?= '$' . $course->price ?>)
                        </button>
                        <?php endif; ?>
                    </div>
                    <?php endif; ?>


                </div>
            </div>
        </div>
    </section>
</main>


<div class="modal fade" id="coursePaymentModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <form id="coursePaymentForm" method="post">
            <input type="hidden" name="course_id" id="enroll_course_id">
            <input type="hidden" name="price" id="enroll_course_price">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Course Payment</h5>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Card Number *</label>
                        <div id="enroll_card_number" class='form-control StripeElement'></div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Expiry Date *</label>
                        <div id="enroll_expiry_date" class='form-control StripeElement'></div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">CVC *</label>
                        <div id="enroll_cvv" class='form-control StripeElement'></div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="submit" class="btn btn-success">Pay & Enroll</button>
                </div>
            </div>
        </form>
    </div>
</div>


<script src="https://js.stripe.com/v3/"></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script>
function openCoursePaymentModal(courseId, price) {
    $('#enroll_course_id').val(courseId);
    $('#enroll_course_price').val(price);
    $('#coursePaymentModal').modal('show');
}
</script>

<script>
$(document).ready(function() {
    const stripe = Stripe(
        'pk_live_51QlU9YGj7vd04uS6VZDoWcCJ7GLQleVHrKfnHOF9yxup31WypzqdS0be1n3n5B6zMCUiGrJvy0gD8uFEbekfHEE000HRvEa6Ie'
    );
    const elements = stripe.elements();

    const style = {
        base: {
            color: '#32325d',
            fontFamily: '"Helvetica Neue", Helvetica, sans-serif',
            fontSmoothing: 'antialiased',
            fontSize: '16px',
            '::placeholder': {
                color: '#aab7c4'
            }
        },
        invalid: {
            color: '#fa755a',
            iconColor: '#fa755a'
        }
    };

    const cardNumber = elements.create('cardNumber', {
        style
    });
    const cardExpiry = elements.create('cardExpiry', {
        style
    });
    const cardCvc = elements.create('cardCvc', {
        style
    });

    cardNumber.mount('#enroll_card_number');
    cardExpiry.mount('#enroll_expiry_date');
    cardCvc.mount('#enroll_cvv');

    $("#coursePaymentForm").on("submit", function(e) {
        e.preventDefault();

        Swal.fire({
            title: 'Processing...',
            allowOutsideClick: false,
            didOpen: () => Swal.showLoading()
        });

        stripe.createPaymentMethod({
            type: 'card',
            card: cardNumber,
            billing_details: {
                name: '<?= $this->session->userdata("name") ?>',
                email: '<?= $this->session->userdata("email") ?>'
            }
        }).then(function(result) {
            if (result.error) {
                Swal.fire("Error", result.error.message, "error");
            } else {
                $.ajax({
                    url: "<?= base_url('course/paid_enroll') ?>",
                    type: "POST",
                    data: {
                        course_id: $("#enroll_course_id").val(),
                        price: $("#enroll_course_price").val(),
                        payment_method_id: result.paymentMethod.id
                    },
                    dataType: "json",
                    success: function(response) {
                        if (response.requires_action) {
                            stripe.confirmCardPayment(response
                                .payment_intent_client_secret).then(function(
                                result) {
                                if (result.error) {
                                    Swal.fire("Error", result.error.message,
                                        "error");
                                } else {
                                    window.location.href =
                                        "<?= base_url('course/my_courses') ?>";
                                }
                            });
                        } else if (response.success) {
                            Swal.fire("Success", "Enrollment Complete", "success")
                                .then(() => {
                                    window.location.href =
                                        "<?= base_url('course/my_courses') ?>";
                                });
                        } else {
                            Swal.fire("Error", response.message, "error");
                        }
                    }
                });
            }
        });
    });
});



</script>
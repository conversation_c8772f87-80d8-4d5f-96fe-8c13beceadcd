<main class="main">

    <!-- Page Title -->
    <div class="page-title" data-aos="fade">
        <div class="heading">
            <div class="container">
                <div class="row d-flex justify-content-center text-center">
                    <div class="col-lg-8">
                        <h1>Course Upload</h1>
                    </div>
                </div>
            </div>
        </div>
        <nav class="breadcrumbs">
            <div class="container">
                <ol>
                    <li><a href="<?=base_url()?>">Home</a></li>
                    <li class="current">Course Upload</li>
                </ol>
            </div>
        </nav>
    </div><!-- End Page Title -->

    <!-- Courses Course Details Section -->
    <section id="courses-course-details" class="courses-course-details section">

        <div class="container" data-aos="fade-up">

            <form method="post" enctype="multipart/form-data"
                action="<?= base_url('course/update/' . $course->id); ?>">
                <div class="form-group">
                    <label>Course Title</label>
                    <input type="text" name="title" class="form-control" value="<?= $course->title; ?>">
                </div>

                <div class="form-group">
                    <label>Description</label>
                    <textarea name="description" class="form-control"><?= $course->description; ?></textarea>
                </div>

                <div class="form-group">
                    <label>Course Thumbnail</label>
                    <input type="file" name="thumbnail" class="form-control">
                    <small>Current: <?= $course->thumbnail; ?></small>
                </div>

                <div class="form-group">
                    <label>Intro Video (YouTube Link or Upload)</label>
                    <input type="text" name="intro_video" class="form-control" value="<?= $course->intro_video; ?>">
                </div>

                <div class="form-group">
                    <label>Full Course Video</label>
                    <input type="text" name="full_video" class="form-control" value="<?= $course->full_video; ?>">
                </div>

                <div class="form-group">
                    <label>Total Duration</label>
                    <input type="text" name="duration" class="form-control" value="<?= $course->duration; ?>">
                </div>

                <div class="form-group">
                    <label>Course Level</label>
                    <select name="level" class="form-control">
                        <option value="Beginner" <?= $course->level == 'Beginner' ? 'selected' : '' ?>>Beginner</option>
                        <option value="Intermediate" <?= $course->level == 'Intermediate' ? 'selected' : '' ?>>
                            Intermediate</option>
                        <option value="Expert" <?= $course->level == 'Expert' ? 'selected' : '' ?>>Expert</option>
                    </select>
                </div>

                <div class="form-group">
                    <label>Target Audience</label>
                    <input type="text" name="target_audience" class="form-control"
                        value="<?= $course->audience; ?>">
                </div>

                <div class="form-group">
                    <label>Language</label>
                    <input type="text" name="language" class="form-control" value="<?= $course->language; ?>">
                </div>

                <div class="form-group">
                    <label>Modules</label>
                    <input type="text" name="modules" class="form-control" value="<?= $course->chapters; ?>">
                </div>

                <div class="form-group">
                    <label>Price</label>
                    <input type="number" name="price" class="form-control" value="<?= $course->price; ?>">
                </div>

                <div class="form-group">
                    <label>Discount Price</label>
                    <input type="number" name="discount_price" class="form-control"
                        value="<?= $course->discount; ?>">
                </div>


                <div class="form-group">
                    <label>Extra File Upload</label>
                    <input type="file" name="extra_file" class="form-control">
                    <small>Current: <?= $course->extra_file; ?></small>
                </div>

                

                <div class="form-check">
                    <input type="checkbox" name="terms_accepted" class="form-check-input"
                        <?= $course->terms_accepted ? 'checked' : '' ?>>
                    <label class="form-check-label">Accept Terms</label>
                </div>

                <button type="submit" class="btn btn-success">Update Course</button>
            </form>



        </div>

    </section>

</main>
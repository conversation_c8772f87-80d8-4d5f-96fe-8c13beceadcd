<main class="main">
    <!-- Page Title -->
    <div class="page-title" data-aos="fade">
        <div class="heading">
            <div class="container">
                <div class="row d-flex justify-content-center text-center">
                    <div class="col-lg-8">
                        <h1>Courses</h1>
                    </div>
                </div>
            </div>
        </div>
        <nav class="breadcrumbs">
            <div class="container">
                <ol>
                    <li><a href="<?= base_url() ?>">Home</a></li>
                    <li class="current">Courses</li>
                </ol>
            </div>
        </nav>
    </div>
    <!-- End Page Title -->

    <section id="courses" class="courses section">
        <div class="container mt-5 mb-4">
            

            <?php if (empty($courses)): ?>
            <div class="alert alert-danger text-center" role="alert">
                No courses available at the moment.
            </div>
            <?php else: ?>
                <form class='w-100 mb-3' action="<?=base_url()?>courses" method="GET">
                <div class="search-bar-container">
                    <input type="text" value="<?= isset($search) ? htmlspecialchars($search) : '' ?>" name='search' id="searchInput" class="search-bar" placeholder="Search Here" required>
                    <button type='submit' class="btn search-button"><i class="fa fa-search"></i> Search</button>
                </div>
            </form>
            <div class="row">
                <?php foreach ($courses as $course): ?>
                <div class="col-lg-4 col-md-6 d-flex align-items-stretch mb-4" data-aos="zoom-in" data-aos-delay="300">
                    <div class="course-item">
                        <img src="<?= base_url('assets/course_thumbnail/' . $course->thumbnail); ?>" class="img-fluid"
                            alt="<?= htmlspecialchars($course->title); ?>">
                        <div class="course-content">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <h4><?= htmlspecialchars($course->level); ?></h4>
                                <p class="price">
                                    <?= $course->price > 0 ? '€' . $course->price : 'Free'; ?>
                                </p>
                            </div>

                            <h3><a
                                    href="<?= base_url('courses/details/' . $course->id); ?>"><?= htmlspecialchars($course->title); ?></a>
                            </h3>

                            <p class="description">
                                <?= character_limiter(strip_tags($course->description), 120); ?>
                            </p>

                            <p><strong>Language:</strong> <?= htmlspecialchars($course->language); ?></p>

                            <div class="mt-2">
                                <a href="<?= base_url('courses/details/' . $course->id); ?>"
                                    class="btn btn-success d-block w-100">
                                    <i class="fa fa-play-circle"></i> View Course
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
            <?php endif; ?>

        </div>
    </section>
</main>
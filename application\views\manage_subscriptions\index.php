<div class="hero page-inner overlay" style="background-image: url('<?=base_url()?>assets/images/manage-bg.jpg'); background-position: center;">
    <div class="container">
        <div class="row justify-content-center align-items-center">
            <div class="col-lg-9 text-center mt-5">
                <h1 class="heading" data-aos="fade-up">Subscriptions History</h1>

                <nav aria-label="breadcrumb" data-aos="fade-up" data-aos-delay="200">
                    <ol class="breadcrumb text-center justify-content-center">
                        <li class="breadcrumb-item"><a href="<?=base_url()?>">Home</a></li>
                        <li class="breadcrumb-item active text-white-50" aria-current="page">
                            Subscription History
                        </li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>
</div>

<div class="section">
    <div class="container">
        <h2 class='font-weight-bold text-primary text-center heading mb-3'>Subscriptions</h2>
        <table class="table table-striped">
            <thead>
                <tr>
                    <th>Subscription ID</th>
                    <th>Plan</th>
                    <th>Start Date</th>
                    <th>End Date</th>
                    <th>Status</th>
                    <th>Price</th>
                </tr>
            </thead>
            <tbody>
                <?php if (!empty($subscriptions)) : ?>
                <?php foreach ($subscriptions as $subscription) : ?>
                <tr>
                    <td><?= $subscription['id'] ?></td>
                    <td><?= $subscription['plan'] ?></td>
                    <td><?= date('Y-m-d', strtotime($subscription['start_date'])) ?></td>
                    <td><?= date('Y-m-d', strtotime($subscription['end_date'])) ?></td>
                    <td><?= ucfirst($subscription['status']) ?></td>
                    <td>€<?= number_format($subscription['price'], 2) ?></td>
                </tr>
                <?php endforeach; ?>
                <?php else : ?>
                <tr>
                    <td colspan="6">No subscriptions found.</td>
                </tr>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
</div>
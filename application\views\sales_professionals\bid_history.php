<main class="main">

    <!-- Page Title -->
    <div class="page-title" data-aos="fade">
        <div class="heading">
            <div class="container">
                <div class="row d-flex justify-content-center text-center">
                    <div class="col-lg-8">
                        <h1>Applications</h1>
                    </div>
                </div>
            </div>
        </div>
        <nav class="breadcrumbs">
            <div class="container">
                <ol>
                    <li><a href="<?=base_url()?>">Home</a></li>
                    <li class="current">Application</li>
                </ol>
            </div>
        </nav>
    </div><!-- End Page Title -->

    <div class="container mt-5">
        <h3 class="text-center mb-4">Application</h3>

        <?php if (empty($bids)): ?>
        <div class="alert alert-info text-center" role="alert">
            You have not placed any applications yet.
        </div>
        <?php else: ?>
        <div class="row">
            <?php foreach ($bids as $bid): ?>
            <div class="col-md-12">
                <div class="card mb-3">
                    <div class="card-body">
                        <h5 class="card-title"><b><?= $bid->assignmentTitle; ?></b></h5>
                        <p class="card-text"><?= $bid->assignmentDesc; ?></p>
                        <p class="card-text"><small class="text-muted">Budget: $<?= $bid->budget; ?></small></p>
                        <p class="card-text"><small class="text-muted">Bid Amount: $<?= $bid->amount; ?></small></p>
                        <p class="card-text"><?= $bid->bid_description; ?></p>
                        <p class="card-text"><small class="text-muted">Bid Date:
                                <?= date('d M Y, H:i', strtotime($bid->created_at)); ?></small></p>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
        <?php endif; ?>
    </div>
</main>
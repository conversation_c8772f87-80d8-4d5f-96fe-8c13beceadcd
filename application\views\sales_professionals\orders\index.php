<main>
    <div class="page-title" data-aos="fade">
        <div class="heading">
            <div class="container">
                <div class="row d-flex justify-content-center text-center">
                    <div class="col-lg-8">
                        <h1>My Assignments History</h1>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="section">
        <div class="container my-5">
            <h2 class="text-center mb-4">My Assignments</h2>
            <ul class="nav nav-tabs" id="orderTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="in-progress-tab" data-bs-toggle="tab" data-bs-target="#in-progress" type="button" role="tab">In Progress</button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="completed-tab" data-bs-toggle="tab" data-bs-target="#completed" type="button" role="tab">Completed</button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="cancelled-tab" data-bs-toggle="tab" data-bs-target="#cancelled" type="button" role="tab">Cancelled</button>
                </li>
            </ul>
            <div class="tab-content mt-4" id="orderTabsContent">
                <?php 
                $statuses = ['in_progress' => $in_progress, 'completed' => $completed, 'cancelled' => $cancelled];
                foreach ($statuses as $status => $orders): ?>
                <div class="tab-pane fade <?= $status == 'in_progress' ? 'show active' : ''; ?>" id="<?= $status; ?>" role="tabpanel">
                    <?php if (empty($orders)): ?>
                        <div class="alert alert-info">No assignments history.</div>
                    <?php else: ?>
                        <?php foreach ($orders as $order): ?>
                            <div class="card mb-3 p-3 border d-flex flex-row align-items-center">
                                <!-- Left: Business Image and Name -->
                                <div class="me-3 text-center">
                                    <img src="<?= base_url('assets/img/business/'.$order->business_image); ?>" class="rounded-circle" width="80" height="80" alt="<?= $order->business_name; ?>">
                                    <p><?= $order->business_name; ?></p>
                                </div>

                                <!-- Center: Order Title and Budget -->
                                <div class="flex-grow-1">
                                    <h5 class="card-title"><?= $order->title; ?></h5>
                                    <p class="card-text">Budget: $<?= $order->amount; ?></p>
                                </div>

                                <!-- Right: Deadline and Delivery Upload -->
                                <div class="text-end">
                                    <p>Deadline: <?= date('d M Y', strtotime($order->deadline)); ?></p>
                                    <?php if ($status == 'in_progress'): ?>
                                        <!-- Check if delivery is empty -->
                                        <?php if (empty($order->delivery)): ?>
                                            <!-- Delivery Upload Form -->
                                            <form class="delivery-form" data-id="<?= $order->id; ?>" enctype="multipart/form-data">
                                                <input type="file" name="delivery_file" class="form-control mb-2" required>
                                                <button type="submit" class="btn btn-success btn-sm">Upload Delivery</button>
                                            </form>
                                        <?php else: ?>
                                            <!-- Delivery already uploaded -->
                                            <a href="<?= base_url('assets/deliveries/'.$order->delivery); ?>" class="btn btn-info btn-sm" download>
                                                <i class="fa fa-download"></i> Download Delivery
                                            </a>
                                        <?php endif; ?>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
</main>


<script>
    $(document).on('submit', '.delivery-form', function(e) {
    e.preventDefault();
    var form = $(this);
    var orderId = form.data('id');
    var formData = new FormData(this);
    formData.append('order_id', orderId);

    $.ajax({
        url: '<?= base_url("SalesProfessional/upload_delivery"); ?>', // Update the URL to your upload method
        type: 'POST',
        data: formData,
        contentType: false,
        processData: false,
        success: function(response) {
               let data = JSON.parse(response)
            if (data.status == 'success') {
                Swal.fire({
                    icon: 'success',
                    title: 'Delivery Uploaded!',
                    text: 'Your delivery has been uploaded successfully.',
                    showConfirmButton: false,
                    timer: 2000
                });
                setTimeout(function() {
                    location.reload(); // Refresh page to update delivery status
                }, 2000);
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Error!',
                    text: data.message,
                });
            }
        }
    });
});

</script>
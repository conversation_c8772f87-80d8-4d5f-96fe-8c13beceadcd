<main>
    <!-- Page Title -->
    <div class="page-title" data-aos="fade">
        <div class="heading">
            <div class="container">
                <div class="row d-flex justify-content-center text-center">
                    <div class="col-lg-8">
                        <h1>Profile</h1>
                        
                    </div>
                </div>
            </div>
        </div>
        <nav class="breadcrumbs">
            <div class="container">
                <ol>
                    <li><a href="<?=base_url()?>">Home</a></li>
                    <li class="current">Profile</li>
                </ol>
            </div>
        </nav>
    </div><!-- End Page Title -->

    <div class="section">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-8" data-aos="fade-up" data-aos-delay="200">
                    <form action="<?= site_url('SalesProfessional/editProfile') ?>" enctype="multipart/form-data"
                        method="post">
                        <?php
                    if($this->session->flashdata('update_error'))
                    {
                        echo '
                        <div class="alert alert-danger">
                        '.$this->session->flashdata("update_error").'
                        </div>
                        ';
                    }
                ?>
                        <?php
                    if($this->session->flashdata('update_success'))
                    {
                        echo '
                        <div class="alert alert-success">
                        '.$this->session->flashdata("update_success").'
                        </div>
                        ';
                    }
                ?>
                        <div class="row">
                            <div class="col-md-4 border-right">
                                <div class="d-flex flex-column align-items-center text-center p-3 py-5">
                                    <img id="profileImage" class="rounded-circle mt-5" width="150px"
                                        src="<?= base_url('assets/img/sales/' . ($sales_professional->profile_picture ?? 'default.png')); ?>">
                                    <input type="file" id="imageUpload" name="profile_photo" class="d-none"
                                        accept="image/*">
                                    <button class="btn btn-outline-secondary mt-3" type="button"
                                        id="uploadButton">Upload Image</button>


                                </div>
                            </div>
                            <div class="col-md-8">
                                <div class="p-3 py-5">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h4 class="text-right">Profile Settings</h4>
                                        <button class="btn btn-outline-success btn-sm" type="button"
                                            onclick="openPromotionModal('freelancer_profile', <?= $sales_professional->id?>)">Promote</button>
                                    </div>

                                    <div class="row mt-2">
                                        <div class="col-md-12">
                                            <label for="referral_code">Referral Code</label>
                                            <div class="input-group">
                                                <input type="text" class="form-control" id="referral_code"
                                                    name="referral_code"
                                                    value="<?= set_value('referral_code', $sales_professional->referral_code); ?>"
                                                    readonly>
                                                <span class="input-group-btn">
                                                    <button class="btn btn-default" type="button" id="copyReferralCode"
                                                        data-toggle="tooltip" title="Copy">
                                                        <i class="fa fa-copy"></i>
                                                    </button>
                                                </span>
                                            </div>
                                        </div>
                                    </div>


                                    <div class="row mt-2">
                                        <div class="col-md-12">
                                            <label for="full_name">Full Name</label>
                                            <input type="text" class="form-control" id="full_name" name="full_name"
                                                value="<?= set_value('full_name', $sales_professional->full_name); ?>"
                                                required>
                                        </div>
                                    </div>
                                    <div class="row mt-3">
                                        <div class="col-md-12">
                                            <label class="labels">Industry</label>
                                            <select class="form-control" id="businessIndustry" name="industry" required>
                                                <option value="" disabled>Select Industry</option>
                                                <option value="Information Technology"
                                                    <?= $sales_professional->industry == 'Information Technology' ? 'selected' : ''; ?>>
                                                    Information Technology</option>
                                                <option value="Healthcare"
                                                    <?= $sales_professional->industry == 'Healthcare' ? 'selected' : ''; ?>>
                                                    Healthcare</option>
                                                <option value="Finance"
                                                    <?= $sales_professional->industry == 'Finance' ? 'selected' : ''; ?>>
                                                    Finance
                                                </option>
                                                <option value="Education"
                                                    <?= $sales_professional->industry == 'Education' ? 'selected' : ''; ?>>
                                                    Education</option>
                                                <option value="Manufacturing"
                                                    <?= $sales_professional->industry == 'Manufacturing' ? 'selected' : ''; ?>>
                                                    Manufacturing</option>
                                                <option value="Retail"
                                                    <?= $sales_professional->industry == 'Retail' ? 'selected' : ''; ?>>
                                                    Retail
                                                </option>
                                                <option value="Construction"
                                                    <?= $sales_professional->industry == 'Construction' ? 'selected' : ''; ?>>
                                                    Construction</option>
                                                <option value="Transportation"
                                                    <?= $sales_professional->industry == 'Transportation' ? 'selected' : ''; ?>>
                                                    Transportation</option>
                                                <option value="Entertainment"
                                                    <?= $sales_professional->industry == 'Entertainment' ? 'selected' : ''; ?>>
                                                    Entertainment</option>
                                                <option value="Other"
                                                    <?= $sales_professional->industry == 'Other' ? 'selected' : ''; ?>>
                                                    Other
                                                </option>
                                            </select>
                                        </div>



                                        <div class="col-md-12 mt-3">
                                            <label for="businessEmail" class="form-label">Email</label>
                                            <input type="email" class="form-control" id="businessEmail" name="email"
                                                placeholder="Email" readonly value="<?= $sales_professional->email; ?>">
                                        </div>
                                        <div class="col-md-12 mt-3">
                                            <label for="kvk-vat" class="form-label">KVK/VAT Number</label>
                                            <input type="text" class="form-control" id="kvk-vat"
                                                name="kvk_vat" value="<?= $sales_professional->kvk_vat; ?>" required>
                                        </div>
                                        <div class="col-md-12 mt-3">
                                            <label for="location">Location</label>
                                            <input type="text" class="form-control" id="location" name="location"
                                                value="<?= set_value('location', $sales_professional->location); ?>"
                                                required>
                                        </div>
                                        <div class="col-md-12 mt-3">
                                            <label for="experience">Overview & Experience</label>
                                            <textarea class="form-control" id="experience" name="experience" rows="3"
                                                required><?= set_value('experience', $sales_professional->experience); ?></textarea>
                                        </div>
                                        <div class="col-md-12 mt-3">
                                            <label for="specialties">Specialties</label>
                                            <textarea class="form-control" id="specialties" name="specialties" rows="3"
                                                required><?= set_value('specialties', $sales_professional->specialties); ?></textarea>
                                        </div>
                                        <div class="col-md-12 mt-3">
                                            <label for="achievements">Achievements</label>
                                            <textarea class="form-control" id="achievements" name="achievements"
                                                rows="3"
                                                required><?= set_value('achievements', $sales_professional->achievements); ?></textarea>
                                        </div>
                                        <div class="col-md-12 mt-3">
                                            <label for="portfolio">Portfolio</label>
                                            <textarea class="form-control" id="portfolio" name="portfolio" rows="3"
                                                required><?= set_value('portfolio', $sales_professional->portfolio); ?></textarea>
                                        </div>
                                    </div>

                                    <div class="mt-5 text-center">
                                        <button class="btn btn-success w-100 d-block" type="submit">Save
                                            Profile</button>
                                    </div>

                                </div>
                            </div>
                        </div>

                    </form>
                </div>
            </div>
        </div>
    </div>
</main>

<div class="modal fade" id="promotionModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <form id="promotionForm" method="post">
            <input type="hidden" name="promotion_type" id="promotion_type">
            <input type="hidden" name="reference_id" id="reference_id">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Choose Promotion Package</h5>
                </div>
                <div class="modal-body">
                    <?php foreach ($promotion_settings as $setting): ?>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="setting_id" value="<?= $setting->id ?>"
                            required>
                        <label class="form-check-label">
                            <?= ucfirst(str_replace('_', ' ', $setting->type)) ?> - <?= $setting->duration_days ?> days
                            - $<?= $setting->price ?>
                        </label>
                    </div>
                    <?php endforeach; ?>
                    <div class="mb-3">
                        <label for="card_number" class="form-label">Card Number *</label>
                        <div id="card_number" class='form-control' class="StripeElement"></div>
                    </div>
                    <div class="mb-3">
                        <label for="expiry_date" class="form-label">Expiry Date *</label>
                        <div id="expiry_date" class='form-control' class="StripeElement"></div>
                    </div>
                    <div class="mb-3">
                        <label for="cvv" class="form-label">CVC *</label>
                        <div id="cvv" class='form-control' class="StripeElement"></div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="submit" class="btn btn-success">Continue to Payment</button>
                </div>
            </div>
        </form>
    </div>
</div>

<script src="https://js.stripe.com/v3/"></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script>
$(document).ready(function() {
    $('#uploadButton').click(function() {
        $('#imageUpload').click();
    });

    $('#imageUpload').change(function() {
        const file = this.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(event) {
                $('#profileImage').attr('src', event.target.result);
            };
            reader.readAsDataURL(file);
        }
    });
});
</script>

<script>
$(document).ready(function() {
    $('#copyReferralCode').click(function() {
        var copyText = document.getElementById("referral_code");
        copyText.select();
        document.execCommand("copy");

        // Optional: Tooltip or alert
        $(this).tooltip('hide')
            .attr('data-original-title', 'Copied!')
            .tooltip('show');

        // Reset tooltip after 1.5 seconds
        setTimeout(() => {
            $(this).attr('data-original-title', 'Copy');
        }, 1500);
    });
});
</script>
<script>
function openPromotionModal(type, referenceId) {
    $('#promotion_type').val(type);
    $('#reference_id').val(referenceId);
    $('#promotionModal').modal('show');
}
</script>

<script>
$(document).ready(function () {
    const stripe = Stripe('pk_live_51QlU9YGj7vd04uS6VZDoWcCJ7GLQleVHrKfnHOF9yxup31WypzqdS0be1n3n5B6zMCUiGrJvy0gD8uFEbekfHEE000HRvEa6Ie');
    const elements = stripe.elements();

    const style = {
        base: {
            color: '#32325d',
            fontFamily: '"Helvetica Neue", Helvetica, sans-serif',
            fontSmoothing: 'antialiased',
            fontSize: '16px',
            '::placeholder': { color: '#aab7c4' }
        },
        invalid: {
            color: '#fa755a',
            iconColor: '#fa755a'
        }
    };

    const cardNumber = elements.create('cardNumber', { style });
    const cardExpiry = elements.create('cardExpiry', { style });
    const cardCvc = elements.create('cardCvc', { style });

    cardNumber.mount('#card_number');
    cardExpiry.mount('#expiry_date');
    cardCvc.mount('#cvv');

    $("#promotionForm").on("submit", function (e) {
        e.preventDefault();

        Swal.fire({
            title: "Confirm Promotion",
            text: "Do you want to proceed with this promotion package?",
            icon: "question",
            showCancelButton: true,
            confirmButtonText: "Yes, Proceed",
        }).then((result) => {
            if (result.isConfirmed) {
                Swal.fire({
                    title: "Processing Payment...",
                    html: "Please wait while your payment is processed.",
                    allowOutsideClick: false,
                    showConfirmButton: false,
                    willOpen: () => {
                        Swal.showLoading();
                    }
                });

                const selectedPackageId = $("input[name='setting_id']:checked").val();
                const promotionType = $("#promotion_type").val();
                const referenceId = $("#reference_id").val();

                stripe.createPaymentMethod({
                    type: "card",
                    card: cardNumber,
                    billing_details: {
                        name: `<?= $this->session->userdata('name')?>`,
                        email: `<?= $this->session->userdata('email')?>`,
                    }
                }).then(function (result) {
                    if (result.error) {
                        Swal.fire("Error!", result.error.message, "error");
                    } else {
                        $.ajax({
                            url: "<?= base_url('promotion/order') ?>",
                            type: "POST",
                            data: {
                                setting_id: selectedPackageId,
                                promotion_type: promotionType,
                                reference_id: referenceId,
                                payment_method_id: result.paymentMethod.id
                            },
                            dataType: "json",
                            success: function (response) {
                                if (response.requires_action) {
                                    handlePromotionAuthentication(response.payment_intent_client_secret);
                                } else if (response.success === true) {
                                    Swal.fire("Success!", "Payment Successful!", "success")
                                        .then(() => { location.reload(); });
                                } else {
                                    Swal.fire("Error!", response.message, "error");
                                }
                            }
                        });
                    }
                });
            }
        });
    });

    function handlePromotionAuthentication(clientSecret) {
        stripe.confirmCardPayment(clientSecret).then(function (result) {
            if (result.error) {
                Swal.close();
                Swal.fire("Error!", result.error.message, "error");
            } else if (result.paymentIntent.status === 'succeeded') {
                $.post("<?= base_url('promotion/confirm_payment') ?>", {
                    payment_intent_id: result.paymentIntent.id
                }, function (resp) {
                    if (resp.success) {
                        Swal.fire("Success!", "Promotion Applied Successfully", "success")
                            .then(() => location.reload());
                    } else {
                        Swal.fire("Error!", resp.error, "error");
                    }
                }, 'json');
            }
        });
    }
});
</script>

<script>
$('#pricing_type').on('change', function() {
    const val = $(this).val();
    $('#hourly_fields, #bonus_fields, #commission_fields').hide();
    if (val === 'hourly') $('#hourly_fields').show();
    if (val === 'hourly_bonus') {
        $('#hourly_fields').show();
        $('#bonus_fields').show();
    }
    if (val === 'commission') $('#commission_fields').show();
}).trigger('change');
</script>
<main>
    <!-- Page Title -->
    <div class="page-title" data-aos="fade">
        <div class="heading">
            <div class="container">
                <div class="row d-flex justify-content-center text-center">
                    <div class="col-lg-8">
                        <h1>Withdraw</h1>
                    </div>
                </div>
            </div>
        </div>
        <nav class="breadcrumbs">
            <div class="container">
                <ol>
                    <li><a href="<?=base_url()?>">Home</a></li>
                    <li class="current">Withdraw</li>
                </ol>
            </div>
        </nav>
    </div><!-- End Page Title -->

    <div class="section">
        <div class="container mt-4">
            <?php if ($this->session->flashdata('success')): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <?= $this->session->flashdata('success'); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            <?php elseif ($this->session->flashdata('error')): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <?= $this->session->flashdata('error'); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            <?php endif; ?>

            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Withdraw Balance</h5>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4>Bank Accounts</h4>
                        <a class="btn btn-outline-success" href='<?= base_url('withdraw/start_onboarding') ?>'>Add Bank
                            Account</a>
                    </div>

                    <form method="post" action="<?= base_url('withdraw/process_withdrawal') ?>">
                        <div class="mb-3">
                            <label for="bank_id" class="form-label">Select Bank Account</label>
                            <select name="bank_id" id="bank_id" class="form-select" required>
                                <?php foreach ($bank_accounts as $bank): ?>
                                <option value="<?= $bank->id ?>">
                                    <?= $bank->bank_name ?> (****<?= $bank->last4 ?>)
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="amount" class="form-label">Withdraw Amount (USD)</label>
                            <input type="number" name="amount" id="amount" class="form-control" min="1" step="0.01"
                                max="<?= $balance ?>" required>
                        </div>

                        <div class="d-flex justify-content-between">

                            <button type="submit" class="btn btn-success">Withdraw</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

    </div>
</main>
<main class="main">
    <!-- Page Title -->
    <div class="page-title" data-aos="fade">
        <div class="heading">
            <div class="container">
                <div class="row d-flex justify-content-center text-center">
                    <div class="col-lg-8">
                        <h1>Blogs</h1>
                    </div>
                </div>
            </div>
        </div>
        <nav class="breadcrumbs">
            <div class="container">
                <ol>
                    <li><a href="<?= base_url() ?>">Home</a></li>
                    <li class="current">Blogs</li>
                </ol>
            </div>
        </nav>
    </div>
    <!-- End Page Title -->

    <section id="courses" class="courses section">
        <div class="container mt-5 mb-4">

            <?php if (empty($list)): ?>
                <div class="alert alert-danger text-center" role="alert">
                    No Blogs available at the moment.
                </div>
            <?php else: ?>
                <div class="row">
                    <?php foreach ($list as $item): ?>
                        <div class="col-lg-4 col-md-6 d-flex align-items-stretch mb-4" data-aos="zoom-in" data-aos-delay="300">
                            <div class="course-item">
                                <img src="<?= base_url('assets/img/blog/' . $item->banner); ?>" class="img-fluid" alt="<?= htmlspecialchars($item->title); ?>">
                                <div class="course-content">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <h4>Blog</h4>
                                        <p class="price">
                                            <i class="fa fa-eye"></i> <?= mt_rand(100, 9999) ?>
                                        </p>
                                    </div>

                                    <h3><a href="<?= site_url($item->route); ?>"><?= htmlspecialchars($item->title); ?></a></h3>

                                    <p class="description">
                                        <?= character_limiter(strip_tags($item->description ?? ''), 120); ?>
                                    </p>

                                    <div class="mt-2">
                                        <a href="<?= site_url($item->route); ?>" class="btn btn-success d-block w-100">
                                            <i class="fa fa-book-open"></i> Read More
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
                <div class="d-flex justify-content-center mt-4">
                    <?= $pagination_links ?>
                </div>
            <?php endif; ?>

        </div>
    </section>
</main>

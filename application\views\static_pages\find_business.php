<main class="main">

    <!-- Page Title -->
    <div class="page-title" data-aos="fade">
        <div class="heading">
            <div class="container">
                <div class="row d-flex justify-content-center text-center">
                    <div class="col-lg-8">
                        <h1>Find Business</h1>
                        <p class="mb-0"></p>
                    </div>
                </div>
            </div>
        </div>
        <nav class="breadcrumbs">
            <div class="container">
                <ol>
                    <li><a href="<?=base_url()?>">Home</a></li>
                    <li class="current">Find Business</li>
                </ol>
            </div>
        </nav>
    </div><!-- End Page Title -->

    <div class="container mt-5">
        <!-- Search Bar -->
        <div class="search-bar-container">
            <input type="text" id="searchInput" class="search-bar" placeholder="Search Here">
            <button id="searchButton" class="btn search-button"><i class="fa fa-search"></i> Search</button>
        </div>

        <div class="filters mt-4">
            <select id="industryFilter">
                <option value="">Industry</option>
                <option value="Information Technology">Information Technology</option>
                <option value="Healthcare">Healthcare</option>
                <option value="Finance">Finance</option>
                <option value="Education">Education</option>
                <option value="Manufacturing">Manufacturing</option>
                <option value="Retail">Retail</option>
                <option value="Construction">Construction</option>
                <option value="Transportation">Transportation</option>
                <option value="Entertainment">Entertainment</option>
                <option value="Other">Other</option>

            </select>

            <select id="locationFilter">
                <option value="">Location</option>
                <option value="New York">New York</option>
                <option value="San Francisco">San Francisco</option>
                <option value="Chicago">Chicago</option>

            </select>
        </div>

        <!-- Sales Professionals List -->
        <div class="row my-5" id="businessList">
            <?php
            if($bussiness && count($bussiness) > 0){
                foreach($bussiness as $professional){
                    $profile_picture = !empty($professional->profile_picture) ? $professional->profile_picture : 'default.png';
                    $desc = substr($professional->goals, 0, 120);
                       $name= $professional->business_name;
            ?>

            <div class="col-md-6">
                <div class="card">
                    <div class="row no-gutters">
                        <div class="col-sm-5">
                            <img class="card-img" src="<?=base_url("assets/img/business/" . $profile_picture)?>" alt="Profile Picture">
                </div>
                <div class="col-sm-7">
                    <div class="card-body">
                     <!-- <p class="mb-0 mb-2"><i class="fa fa-star text-warning" aria-hidden="true"></i> 4.7(5)</p> -->
                        <h4><b><?=$name?></b></h4>
                     <p class="card-text"><?=$desc?></p>
                            <a href="<?=base_url('business-details/' . $professional->id)?>"
                                class="btn btn-sm view-profile-button"><i class="fa fa-user" aria-hidden="true"></i>
                                View Profile</a>
                        </div>
                    </div>
                </div>
            </div>
            <?php
                }}
                ?>
        </div>
    </div>
    </div>

</main>

<script>
$(document).ready(function() {
    function fetchProfessionals() {
        const query = $('#searchInput').val().trim();
        const industry = $('#industryFilter').val();
        const location = $('#locationFilter').val();

        Swal.fire({
            title: 'Searching Businessess...',
            text: 'Please wait while we find the best matches for you.',
            allowOutsideClick: false,
            didOpen: () => {
                Swal.showLoading();
            }
        });

        $.ajax({
            url: '<?= base_url("StaticPages/search_business") ?>',
            method: 'POST',
            data: {
                query: query,
                industry: industry,
                location: location
            },
            success: function(data) {
                Swal.close();
                $('#businessList').empty();
                $('#businessList').html(data);
            },
            error: function() {
                Swal.close();
                Swal.fire({
                    icon: 'error',
                    title: 'Oops...',
                    text: 'Something went wrong. Please try again later.',
                });
            }
        });
    }

    $('#searchButton').on('click', fetchProfessionals);
    $('#industryFilter, #locationFilter').on('change', fetchProfessionals);
});
</script>
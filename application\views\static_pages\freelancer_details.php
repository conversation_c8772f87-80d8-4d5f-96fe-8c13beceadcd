<main class="main">
    <!-- Page Title -->
    <div class="page-title" data-aos="fade">
        <div class="heading">
            <div class="container">
                <div class="row d-flex justify-content-center text-center">
                    <div class="col-lg-8">
                        <h1>Freelancer Details</h1>
                        <p class="mb-0"></p>
                    </div>
                </div>
            </div>
        </div>
        <nav class="breadcrumbs">
            <div class="container">
                <ol>
                    <li><a href="<?=base_url()?>">Home</a></li>
                    <li class="current">Freelancer Details</li>
                </ol>
            </div>
        </nav>
    </div><!-- End Page Title -->
    <div class="container my-5 emp-profile">
        <div class="row">
            <div class="col-md-4">
                <div class="profile-img">
                    <img src="<?= base_url('assets/img/sales/' . ($sales_professional->profile_picture ?? 'default.png')); ?>"
                        alt="Sales Professional Image" />
                </div>
            </div>
            <div class="col-md-8">
                <div class="profile-head">
                    <h5>
                        <?=$sales_professional->full_name?>
                    </h5>
                    <h6 class='mt-2'>
                        <i class="fa fa-map-marker" aria-hidden="true"></i> <?=$sales_professional->location?>
                    </h6>
                    <p class="text-muted"><i class="fa fa-industry" aria-hidden="true"></i>
                        <span><?=$sales_professional->industry?></span>
                    </p>
                    <p class="proile-rating">
                        <i class="fa fa-star text-warning" aria-hidden="true"></i>
                        <span><?= $sales_professional->average_rating; ?>
                            (<?= $sales_professional->review_count; ?>)</span>
                    </p>

                    

                    <div>
                        <?php
                            if($this->session->userdata('b_id')){
                        ?>
                        <button class="btn btn-success chat-btn" data-id="<?= $sales_professional->id ?>">Chat</button>
                        <?php
                            }
                            else{
                                ?>
                        <a class="btn btn-success" href='<?=base_url()?>login'>Chat</a>
                        <?php
                            }
                        ?>
                        <a class=' ml-3 btn btn-outline-success'
                            href="mailto:<?=$sales_professional->email?>">Contact</a>
                    </div>
                </div>
            </div>

        </div>

        <div class="row my-5 overview">
            <div class="col-md-4 pr-2">
                <h5>Overview & Experience</h5>
                <p><?=$sales_professional->experience?></p>
            </div>
            <div class="col-md-4 pr-2">
                <h5>Specialities</h5>
                <p><?=$sales_professional->specialties?></p>
            </div>
            <div class="col-md-4">
                <h5>Achievements</h5>
                <p><?=$sales_professional->achievements?></p>
            </div>
        </div>



        <div class='mt-4 overview'>
            <h5>Customer Reviews</h5>

            <?php if (!empty($reviews)): ?>
            <?php foreach ($reviews as $review): ?>
            <div class="card mb-3">
                <div class="row no-gutters">
                    <div class="col-md-2 text-center">
                        <img src="<?= base_url('assets/img/business/' . ($review->profile_picture ?? 'default.png')); ?>"
                            class="rounded-circle" style="width: 80px; height: 80px;"
                            alt="<?= $review->business_name; ?>">
                        <p class="mt-2"><?= $review->business_name; ?></p>
                    </div>
                    <div class="col-md-10">
                        <div class="card-body">
                            <p class="mb-1">
                                <?php for ($i = 1; $i <= 5; $i++): ?>
                                <i
                                    class="fa fa-star <?= ($i <= $review->rating) ? 'text-warning' : 'text-muted'; ?>"></i>
                                <?php endfor; ?>
                                <span class="text-muted">(<?= $review->rating; ?>)</span>
                            </p>
                            <p><?= $review->review; ?></p>
                            <small class="text-muted"><?= date('F j, Y', strtotime($review->created_at)); ?></small>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
            <?php else: ?>
            <p>No reviews yet.</p>
            <?php endif; ?>
        </div>

        <div class="modal fade" id="chatModal" tabindex="-1" aria-labelledby="chatModalLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="chatModalLabel">Start Chat</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <textarea class="form-control" id="chatMessage" rows="3"
                            placeholder="Type your message..."></textarea>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-success" id="sendChatMessage">Send Message</button>
                    </div>
                </div>
            </div>
        </div>

    </div>
</main>


<script>
$(document).ready(function() {
    let selectedUserId = null;
    let businessId = "<?= $this->session->userdata('b_id'); ?>";

    // Open chat modal
    $(".chat-btn").click(function() {
        selectedUserId = $(this).data("id");
        $("#chatModal").modal("show");
    });

    // Send message
    $("#sendChatMessage").click(function() {
        let message = $("#chatMessage").val().trim();
        if (message === "") {
            alert("Please enter a message.");
            return;
        }

        $.ajax({
            url: "<?= base_url('Chat/start_chat') ?>",
            type: "POST",
            data: {
                business_id: businessId,
                sales_professional_id: selectedUserId,
                message: message
            },
            dataType: "json",
            success: function(response) {
                $("#chatModal").modal("hide");
                if (response.status === "exists") {
                    window.location.href = "<?= base_url() ?>/inbox/" + response.chat_id;
                } else if (response.status === "new") {
                    Swal.fire({
                        title: "Message Sent!",
                        text: "Your chat has been started successfully.",
                        icon: "success",
                        confirmButtonText: "Go to Chat"
                    }).then(() => {
                        window.location.href = "<?= base_url() ?>inbox/" + response
                            .chat_id;
                    });
                } else {
                    alert("Something went wrong. Please try again.");
                }
            }
        });
    });
});
</script>
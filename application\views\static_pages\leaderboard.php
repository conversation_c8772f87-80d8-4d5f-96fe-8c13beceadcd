<main class="main">

    <!-- Page Title -->
    <div class="page-title" data-aos="fade">
        <div class="heading">
            <div class="container">
                <div class="row d-flex justify-content-center text-center">
                    <div class="col-lg-8">
                        <h1>LeaderBoard</h1>
                        <p class="mb-0"></p>
                    </div>
                </div>
            </div>
        </div>
        <nav class="breadcrumbs">
            <div class="container">
                <ol>
                    <li><a href="<?= base_url() ?>">Home</a></li>
                    <li class="current">LeaderBoard</li>
                </ol>
            </div>
        </nav>
    </div><!-- End Page Title -->

    <div class="container mt-5">

        <!-- Filters -->
        <div class="text-center mb-4">
            <button class="filter-btn btn btn-outline-success" data-filter="yearly">Yearly</button>
            <button class="filter-btn btn btn-outline-success" data-filter="monthly">Monthly</button>
            <button class="filter-btn btn btn-outline-success" data-filter="weekly">Weekly</button>
        </div>

        <div class="my-5 overview">
            <h4 class='text-center mb-5'>Top Sales Professionals</h4>

            <div id="leaderboard-list">
                <?php if (!empty($top_sales)) : ?>
                    <?php foreach ($top_sales as $index => $sales) : ?>
                        <div class='leaderboard-container'>
                            <div class='basic-info'>
                                <img src="<?= base_url('assets/img/sales/' . ($sales->profile_picture ?? 'default.png')); ?>" alt="">
                                <div>
                                    <h6 class='text-white'><?= $sales->full_name ?></h6>
                                    <p class='text-white'><?= $sales->industry ?></p>
                                </div>
                            </div>
                            <div class='total-sales'>
                                <p class='text-white m-0 pb-2'>Total Sales: <?= $sales->total_sales ?></p>
                                <p class='text-white'>Rating: <?= number_format($sales->average_rating, 1) ?></p>
                            </div>
                            <div class='rank'>
                                <strong class='text-white'>&#129351; <?= str_pad($index + 1, 3, '0', STR_PAD_LEFT) ?></strong>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else : ?>
                    <p class="text-center text-muted">No sales professionals found for the selected period.</p>
                <?php endif; ?>
            </div>
        </div>

    </div>

</main>



<script>
    $(document).ready(function() {
    $('.filter-btn').on('click', function() {
        var filter = $(this).data('filter');

        // Add active class to clicked button and remove from others
        $('.filter-btn').removeClass('btn-primary').addClass('btn-outline-primary');
        $(this).removeClass('btn-outline-primary').addClass('btn-primary');

        // AJAX Request
        $.ajax({
            url: "<?= base_url('StaticPages/get_leaderboard_data') ?>",
            type: "POST",
            data: { filter: filter },
            dataType: "json",
            success: function(data) {
                var leaderboardHTML = '';

                $.each(data, function(index, sales) {
                    leaderboardHTML += `
                        <div class='leaderboard-container'>
                            <div class='basic-info'>
                                <img src="<?= base_url('assets/img/sales/') ?>${sales.profile_picture ?? 'default.png'}" alt="">
                                <div>
                                    <h6 class='text-white'>${sales.full_name}</h6>
                                    <p class='text-white'>${sales.industry}</p>
                                </div>
                            </div>
                            <div class='total-sales'>
                                <p class='text-white m-0 pb-2'>Total Sales: ${sales.total_sales}</p>
                                <p class='text-white'>Rating: ${parseFloat(sales.average_rating).toFixed(1)}</p>
                            </div>
                            <div class='rank'>
                                <strong class='text-white'>&#129351; ${String(index + 1).padStart(3, '0')}</strong>
                            </div>
                        </div>
                    `;
                });

                $('#leaderboard-list').html(leaderboardHTML);
            }
        });
    });
});

</script>
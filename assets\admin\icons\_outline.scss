@charset "UTF-8";

@font-face {
  font-family: "outlined-iconset";
  src:url("../fonts/outlined-iconset.eot");
  src:url("../fonts/outlined-iconset.eot?#iefix") format("embedded-opentype"),
    url("../fonts/outlined-iconset.woff") format("woff"),
    url("../fonts/outlined-iconset.ttf") format("truetype"),
    url("../fonts/outlined-iconset.svg#outlined-iconset") format("svg");
  font-weight: normal;
  font-style: normal;

}

[data-icon]:before {
  font-family: "outlined-iconset" !important;
  content: attr(data-icon);
  font-style: normal !important;
  font-weight: normal !important;
  font-variant: normal !important;
  text-transform: none !important;
  speak: none;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

[class^="icon-"]:before,
[class*=" icon-"]:before {
  font-family: "outlined-iconset" !important;
  font-style: normal !important;
  font-weight: normal !important;
  font-variant: normal !important;
  text-transform: none !important;
  speak: none;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-boat:before {
  content: "\e000";
}
.icon-booknote:before {
  content: "\e001";
}
.icon-booknote-add:before {
  content: "\e002";
}
.icon-booknote-remove:before {
  content: "\e003";
}
.icon-camera-1:before {
  content: "\e004";
}
.icon-camera-2:before {
  content: "\e005";
}
.icon-cloud-check:before {
  content: "\e006";
}
.icon-cloud-delete:before {
  content: "\e007";
}
.icon-cloud-download:before {
  content: "\e008";
}
.icon-cloud-upload:before {
  content: "\e009";
}
.icon-cloudy:before {
  content: "\e00a";
}
.icon-cocktail:before {
  content: "\e00b";
}
.icon-coffee:before {
  content: "\e00c";
}
.icon-compass:before {
  content: "\e00d";
}
.icon-compress:before {
  content: "\e00e";
}
.icon-cutlery:before {
  content: "\e00f";
}
.icon-delete:before {
  content: "\e010";
}
.icon-delete-folder:before {
  content: "\e011";
}
.icon-dialogue-add:before {
  content: "\e012";
}
.icon-dialogue-delete:before {
  content: "\e013";
}
.icon-dialogue-happy:before {
  content: "\e014";
}
.icon-dialogue-sad:before {
  content: "\e015";
}
.icon-dialogue-text:before {
  content: "\e016";
}
.icon-dialogue-think:before {
  content: "\e017";
}
.icon-diamond:before {
  content: "\e018";
}
.icon-dish-fork:before {
  content: "\e019";
}
.icon-dish-spoon:before {
  content: "\e01a";
}
.icon-download:before {
  content: "\e01b";
}
.icon-download-folder:before {
  content: "\e01c";
}
.icon-expand:before {
  content: "\e01d";
}
.icon-eye:before {
  content: "\e01e";
}
.icon-fast-food:before {
  content: "\e01f";
}
.icon-flag:before {
  content: "\e020";
}
.icon-folder:before {
  content: "\e021";
}
.icon-geolocalizator:before {
  content: "\e022";
}
.icon-globe:before {
  content: "\e023";
}
.icon-graph:before {
  content: "\e024";
}
.icon-graph-descending:before {
  content: "\e025";
}
.icon-graph-rising:before {
  content: "\e026";
}
.icon-hammer:before {
  content: "\e027";
}
.icon-happy-drop:before {
  content: "\e028";
}
.icon-headphones:before {
  content: "\e029";
}
.icon-heart:before {
  content: "\e02a";
}
.icon-heart-broken:before {
  content: "\e02b";
}
.icon-home:before {
  content: "\e02c";
}
.icon-hourglass:before {
  content: "\e02d";
}
.icon-image:before {
  content: "\e02e";
}
.icon-key:before {
  content: "\e02f";
}
.icon-life-buoy:before {
  content: "\e030";
}
.icon-list:before {
  content: "\e031";
}
.icon-lock-closed:before {
  content: "\e032";
}
.icon-lock-open:before {
  content: "\e033";
}
.icon-loudspeaker:before {
  content: "\e034";
}
.icon-magnifier:before {
  content: "\e035";
}
.icon-magnifier-minus:before {
  content: "\e036";
}
.icon-magnifier-plus:before {
  content: "\e037";
}
.icon-mail:before {
  content: "\e038";
}
.icon-mail-open:before {
  content: "\e039";
}
.icon-map:before {
  content: "\e03a";
}
.icon-medical-case:before {
  content: "\e03b";
}
.icon-microphone-1:before {
  content: "\e03c";
}
.icon-microphone-2:before {
  content: "\e03d";
}
.icon-minus:before {
  content: "\e03e";
}
.icon-multiple-image:before {
  content: "\e03f";
}
.icon-music-back:before {
  content: "\e040";
}
.icon-music-backtoend:before {
  content: "\e041";
}
.icon-music-eject:before {
  content: "\e042";
}
.icon-music-forward:before {
  content: "\e043";
}
.icon-music-forwardtoend:before {
  content: "\e044";
}
.icon-music-pause:before {
  content: "\e045";
}
.icon-music-play:before {
  content: "\e046";
}
.icon-music-random:before {
  content: "\e047";
}
.icon-music-repeat:before {
  content: "\e048";
}
.icon-music-stop:before {
  content: "\e049";
}
.icon-musical-note:before {
  content: "\e04a";
}
.icon-musical-note-2:before {
  content: "\e04b";
}
.icon-old-video-cam:before {
  content: "\e04c";
}
.icon-paper-pen:before {
  content: "\e04d";
}
.icon-paper-pencil:before {
  content: "\e04e";
}
.icon-paper-sheet:before {
  content: "\e04f";
}
.icon-pen-pencil-ruler:before {
  content: "\e050";
}
.icon-pencil:before {
  content: "\e051";
}
.icon-pencil-ruler:before {
  content: "\e052";
}
.icon-plus:before {
  content: "\e053";
}
.icon-portable-pc:before {
  content: "\e054";
}
.icon-pricetag:before {
  content: "\e055";
}
.icon-printer:before {
  content: "\e056";
}
.icon-profile:before {
  content: "\e057";
}
.icon-profile-add:before {
  content: "\e058";
}
.icon-profile-remove:before {
  content: "\e059";
}
.icon-rainy:before {
  content: "\e05a";
}
.icon-rotate:before {
  content: "\e05b";
}
.icon-setting-1:before {
  content: "\e05c";
}
.icon-setting-2:before {
  content: "\e05d";
}
.icon-share:before {
  content: "\e05e";
}
.icon-shield-down:before {
  content: "\e05f";
}
.icon-shield-left:before {
  content: "\e060";
}
.icon-shield-right:before {
  content: "\e061";
}
.icon-shield-up:before {
  content: "\e062";
}
.icon-shopping-cart:before {
  content: "\e063";
}
.icon-shopping-cart-content:before {
  content: "\e064";
}
.icon-sinth:before {
  content: "\e065";
}
.icon-smartphone:before {
  content: "\e066";
}
.icon-spread:before {
  content: "\e067";
}
.icon-squares:before {
  content: "\e068";
}
.icon-stormy:before {
  content: "\e069";
}
.icon-sunny:before {
  content: "\e06a";
}
.icon-tablet:before {
  content: "\e06b";
}
.icon-three-stripes-horiz:before {
  content: "\e06c";
}
.icon-three-stripes-vert:before {
  content: "\e06d";
}
.icon-ticket:before {
  content: "\e06e";
}
.icon-todolist:before {
  content: "\e06f";
}
.icon-todolist-add:before {
  content: "\e070";
}
.icon-todolist-check:before {
  content: "\e071";
}
.icon-trash-bin:before {
  content: "\e072";
}
.icon-tshirt:before {
  content: "\e073";
}
.icon-tv-monitor:before {
  content: "\e074";
}
.icon-umbrella:before {
  content: "\e075";
}
.icon-upload:before {
  content: "\e076";
}
.icon-upload-folder:before {
  content: "\e077";
}
.icon-variable:before {
  content: "\e078";
}
.icon-video-cam:before {
  content: "\e079";
}
.icon-volume-higher:before {
  content: "\e07a";
}
.icon-volume-lower:before {
  content: "\e07b";
}
.icon-volume-off:before {
  content: "\e07c";
}
.icon-watch:before {
  content: "\e07d";
}
.icon-waterfall:before {
  content: "\e07e";
}
.icon-website-1:before {
  content: "\e07f";
}
.icon-website-2:before {
  content: "\e080";
}
.icon-wine:before {
  content: "\e081";
}
.icon-calendar:before {
  content: "\e082";
}
.icon-alarm-clock:before {
  content: "\e083";
}
.icon-add-folder:before {
  content: "\e084";
}
.icon-accelerator:before {
  content: "\e085";
}
.icon-agenda:before {
  content: "\e086";
}
.icon-arrow-left:before {
  content: "\e087";
}
.icon-arrow-down:before {
  content: "\e088";
}
.icon-battery-1:before {
  content: "\e089";
}
.icon-case:before {
  content: "\e08a";
}
.icon-arrow-up:before {
  content: "\e08b";
}
.icon-arrow-right:before {
  content: "\e08c";
}
.icon-case-2:before {
  content: "\e08d";
}
.icon-cd:before {
  content: "\e08e";
}
.icon-battery-2:before {
  content: "\e08f";
}
.icon-battery-3:before {
  content: "\e090";
}
.icon-check:before {
  content: "\e091";
}
.icon-battery-4:before {
  content: "\e092";
}
.icon-chronometer:before {
  content: "\e093";
}
.icon-clock:before {
  content: "\e094";
}
.icon-blackboard-graph:before {
  content: "\e095";
}
